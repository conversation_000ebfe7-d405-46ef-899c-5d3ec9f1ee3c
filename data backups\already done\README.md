# Product Weight Scraper

A Python script to scrape exact product weights from e-commerce websites, organized by brand and processed in batches.

## Features

- **Brand-wise Processing**: Processes products in batches based on brand (<PERSON><PERSON>, <PERSON>rej, Qubo, Tagus)
- **Multi-site Scraping**: Searches across Amazon, Flipkart, and Snapdeal
- **Intelligent Weight Extraction**: Uses multiple patterns to find weight information
- **Rate Limiting**: Built-in delays and request throttling to avoid being blocked
- **Concurrent Processing**: Uses ThreadPoolExecutor for efficient scraping
- **Comprehensive Logging**: Detailed logs for monitoring progress
- **JSON Output**: Results stored in structured JSON format
- **Error Handling**: Robust error handling and recovery

## Installation

1. Install required dependencies:
```bash
pip install -r requirements.txt
```

2. Ensure your CSV file is in the same directory as the script

## Usage

### Basic Usage
```bash
python product_weight_scraper.py
```

### CSV File Format
The script expects a CSV file named `products_export_20250709_161453.csv` with columns:
- Name: Product name
- Category: Product category
- Subcategory: Product subcategory  
- Brand: Product brand
- Price: Product price

## Output Files

### Main Results
- `product_weights_YYYYMMDD_HHMMSS.json`: Complete results with all products
- `product_weights_YYYYMMDD_HHMMSS_summary.txt`: Summary statistics

### Brand-specific Results
- `weights_haier_YYYYMMDD_HHMMSS.json`: Haier products only
- `weights_godrej_YYYYMMDD_HHMMSS.json`: Godrej products only
- `weights_qubo_YYYYMMDD_HHMMSS.json`: Qubo products only
- `weights_tagus_YYYYMMDD_HHMMSS.json`: Tagus products only

### Log Files
- `scraper.log`: Detailed execution logs

## JSON Output Structure

```json
{
  "scraping_info": {
    "total_products": 320,
    "products_with_weight": 150,
    "scraped_at": "2025-07-09T16:30:00",
    "brands_processed": ["Haier", "Godrej", "Qubo", "Tagus"]
  },
  "products_by_brand": {
    "Haier": [
      {
        "name": "Haier Hood Vision Series...",
        "category": "Hood",
        "subcategory": "Vision Series",
        "brand": "Haier",
        "price": 24990,
        "weight": "15.5 kg",
        "source_url": "amazon",
        "scraped_at": "2025-07-09T16:25:30"
      }
    ]
  }
}
```

## Configuration

### Adjustable Parameters

In the `ProductWeightScraper` class:

- `max_workers`: Number of concurrent threads (default: 2-3)
- `batch_size`: Products processed per batch (default: 10)
- `timeout`: Request timeout in seconds (default: 10)
- `delay_range`: Random delay between requests (1-3 seconds)

### Adding New Sites

To add new e-commerce sites, update the `search_sites` dictionary:

```python
'new_site': {
    'url': 'https://example.com/search?q={query}',
    'weight_selectors': [
        '.weight-info',
        '[data-weight]',
        '.product-specs:contains("Weight")'
    ]
}
```

## Weight Detection Patterns

The script detects weights in various formats:
- Kilograms: "5.2 kg", "3 kilograms"
- Grams: "500 g", "1200 grams", "800 gm"
- Pounds: "10 lb", "5.5 lbs", "3 pounds"
- Ounces: "16 oz", "8 ounces"

## Rate Limiting & Ethics

- Random delays between requests (1-10 seconds)
- Respectful request patterns
- User-agent rotation
- Batch processing to avoid overwhelming servers
- Comprehensive error handling

## Troubleshooting

### Common Issues

1. **No weights found**: 
   - Check if product names are too generic
   - Verify internet connection
   - Sites may have changed their structure

2. **Rate limiting errors**:
   - Increase delays in the script
   - Reduce batch size
   - Reduce max_workers

3. **CSV parsing errors**:
   - Ensure CSV has correct column names
   - Check for encoding issues (use UTF-8)

### Logs Analysis

Check `scraper.log` for detailed information:
- Search queries being used
- Sites being accessed
- Weight extraction attempts
- Error messages

## Performance Tips

1. **Optimize search queries**: The script automatically cleans product names
2. **Monitor success rates**: Check summary files for effectiveness
3. **Adjust delays**: Balance speed vs. reliability
4. **Use intermediate saves**: Brand-specific files allow resuming

## Legal Considerations

- Respect robots.txt files
- Follow website terms of service
- Use reasonable request rates
- Consider API alternatives when available

## Support

For issues or improvements:
1. Check the log files for error details
2. Verify CSV file format
3. Test with a smaller subset first
4. Monitor network connectivity
