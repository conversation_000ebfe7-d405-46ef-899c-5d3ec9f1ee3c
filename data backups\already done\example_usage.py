#!/usr/bin/env python3
"""
Example usage of the Product Weight Scraper
Demonstrates different ways to use the scraper
"""

import os
import json
from datetime import datetime
from product_weight_scraper import ProductWeightScraper, Product

def example_1_basic_usage():
    """Example 1: Basic usage with default settings"""
    print("Example 1: Basic Usage")
    print("-" * 30)
    
    # Check if CSV file exists
    csv_file = "products_export_20250709_161453.csv"
    if not os.path.exists(csv_file):
        print(f"CSV file not found: {csv_file}")
        print("Please ensure the CSV file is in the current directory")
        return
    
    # Initialize scraper
    scraper = ProductWeightScraper(csv_file)
    
    # Load products
    scraper.load_products()
    print(f"Loaded {len(scraper.products)} products")
    
    # Show available brands
    brands = list(set(product.brand for product in scraper.products))
    print(f"Available brands: {brands}")
    
    # Show sample products for each brand
    for brand in brands:
        brand_products = [p for p in scraper.products if p.brand == brand]
        print(f"\n{brand} ({len(brand_products)} products):")
        for i, product in enumerate(brand_products[:3], 1):
            print(f"  {i}. {product.name[:50]}...")

def example_2_single_brand():
    """Example 2: Process only one brand"""
    print("\nExample 2: Single Brand Processing")
    print("-" * 40)
    
    csv_file = "products_export_20250709_161453.csv"
    if not os.path.exists(csv_file):
        print(f"CSV file not found: {csv_file}")
        return
    
    scraper = ProductWeightScraper(csv_file)
    scraper.load_products()
    
    # Process only Haier products
    brand = "Haier"
    print(f"Processing {brand} products only...")
    
    # You can uncomment the line below to actually run the scraping
    # results = scraper.process_brand_batch(brand, max_workers=1)
    # print(f"Processed {len(results)} {brand} products")

def example_3_custom_configuration():
    """Example 3: Using custom configuration"""
    print("\nExample 3: Custom Configuration")
    print("-" * 35)
    
    csv_file = "products_export_20250709_161453.csv"
    if not os.path.exists(csv_file):
        print(f"CSV file not found: {csv_file}")
        return
    
    scraper = ProductWeightScraper(csv_file)
    
    # Customize configuration
    scraper.config = {
        'max_workers': 1,  # Use only 1 worker for slower, more respectful scraping
        'batch_size': 5,   # Process 5 products at a time
        'request_timeout': 15,  # Longer timeout
        'request_delay_min': 2,  # Longer delays
        'request_delay_max': 5,
        'batch_delay_min': 10,
        'batch_delay_max': 15,
        'max_product_pages': 2  # Check fewer pages per product
    }
    
    print("Custom configuration:")
    for key, value in scraper.config.items():
        print(f"  {key}: {value}")
    
    scraper.load_products()
    print(f"Ready to process {len(scraper.products)} products with custom settings")

def example_4_weight_extraction_test():
    """Example 4: Test weight extraction on sample text"""
    print("\nExample 4: Weight Extraction Testing")
    print("-" * 38)
    
    scraper = ProductWeightScraper()
    
    # Sample product descriptions with weight information
    sample_texts = [
        "This washing machine weighs 65 kg and has a capacity of 8 kg",
        "Product dimensions: 60x60x85 cm, Net weight: 45.5 kilograms",
        "Item weight: 2.3 pounds, shipping weight: 3.1 lbs",
        "Weight: 1200g, Color: White, Material: Plastic",
        "Gross weight 15.7 kg, Net weight 14.2 kg",
        "No weight information in this description",
        "The product weighs approximately 800 grams"
    ]
    
    print("Testing weight extraction:")
    for i, text in enumerate(sample_texts, 1):
        weight = scraper.extract_weight_from_text(text)
        print(f"{i}. Text: {text[:50]}...")
        print(f"   Extracted: {weight or 'No weight found'}")
        print()

def example_5_product_name_cleaning():
    """Example 5: Test product name cleaning"""
    print("\nExample 5: Product Name Cleaning")
    print("-" * 33)
    
    scraper = ProductWeightScraper()
    
    # Sample product names (from the CSV)
    sample_names = [
        "Haier Hood Vision Series T-Shaped Tpe T-Shaped Motor CopperMotor Motor CopperMotor MaximumAirFlow HIH-T90HM-V",
        "Qubo E27 LED Wi-Fi BT 16 Million Colours Voice Control Alexa OK Google Smart Bulb 2",
        "Nav-tal 6 Levers - Long Shackle (2 keys) Furniture Locks Furniture Locks",
        "Qubo HTAB11NZ01 Bike|AntiTheft,Towing & Accident Alert|Installation Help|1 Month Plan GPS Device (Black)"
    ]
    
    print("Testing product name cleaning:")
    for i, name in enumerate(sample_names, 1):
        cleaned = scraper.clean_product_name(name)
        print(f"{i}. Original: {name}")
        print(f"   Cleaned:  {cleaned}")
        print()

def example_6_manual_product_scraping():
    """Example 6: Manually create and scrape a single product"""
    print("\nExample 6: Manual Product Scraping")
    print("-" * 35)
    
    # Create a test product manually
    test_product = Product(
        name="Samsung 8kg Front Load Washing Machine",
        category="Washing Machines",
        subcategory="Front Load",
        brand="Samsung",
        price=35000
    )
    
    print(f"Test product: {test_product.name}")
    print(f"Brand: {test_product.brand}")
    print(f"Price: ₹{test_product.price}")
    
    scraper = ProductWeightScraper()
    cleaned_name = scraper.clean_product_name(test_product.name)
    print(f"Cleaned name for search: {cleaned_name}")
    
    # You can uncomment the line below to actually scrape
    # result = scraper.scrape_product_weight(test_product)
    # print(f"Scraped weight: {result.weight}")

def example_7_results_analysis():
    """Example 7: Analyze existing results"""
    print("\nExample 7: Results Analysis")
    print("-" * 27)
    
    # Look for existing result files
    result_files = [f for f in os.listdir('.') if f.startswith('weights_') and f.endswith('.json')]
    
    if not result_files:
        print("No result files found. Run the scraper first to generate results.")
        return
    
    print(f"Found {len(result_files)} result files:")
    for file in result_files:
        print(f"  - {file}")
    
    # Analyze the most recent file
    if result_files:
        latest_file = max(result_files, key=os.path.getctime)
        print(f"\nAnalyzing latest file: {latest_file}")
        
        try:
            with open(latest_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if 'products' in data:  # Brand-specific file
                products = data['products']
                with_weight = len([p for p in products if p.get('weight')])
                print(f"Brand: {data.get('brand', 'Unknown')}")
                print(f"Total products: {len(products)}")
                print(f"Products with weight: {with_weight}")
                print(f"Success rate: {with_weight/len(products)*100:.1f}%")
                
            elif 'products_by_brand' in data:  # Combined file
                print("Combined results file:")
                for brand, products in data['products_by_brand'].items():
                    with_weight = len([p for p in products if p.get('weight')])
                    print(f"  {brand}: {with_weight}/{len(products)} ({with_weight/len(products)*100:.1f}%)")
        
        except Exception as e:
            print(f"Error reading file: {e}")

def main():
    """Run all examples"""
    print("=" * 60)
    print("PRODUCT WEIGHT SCRAPER - USAGE EXAMPLES")
    print("=" * 60)
    
    # Run examples that don't require actual scraping
    example_1_basic_usage()
    example_2_single_brand()
    example_3_custom_configuration()
    example_4_weight_extraction_test()
    example_5_product_name_cleaning()
    example_6_manual_product_scraping()
    example_7_results_analysis()
    
    print("\n" + "=" * 60)
    print("EXAMPLES COMPLETED")
    print("=" * 60)
    print("\nTo actually run the scraper:")
    print("1. Basic usage: python product_weight_scraper.py")
    print("2. Specific brand: python run_brand_batch.py --brands Haier")
    print("3. Test mode: python run_brand_batch.py --brands Haier --dry-run")
    print("4. Custom settings: python run_brand_batch.py --brands Haier --workers 1 --batch-size 5")

if __name__ == "__main__":
    main()
