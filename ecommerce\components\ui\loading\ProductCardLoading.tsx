import React from "react";

const ProductCardLoading = () => {
  return (
    <div className="w-full bg-card shadow rounded-lg p-4 border border-border animate-pulse">
      <div className="w-full h-[200px] bg-muted rounded-md"></div>
      <div className="mt-4 text-left">
        <div className="h-4 bg-muted rounded w-3/4 mt-2"></div>
        <div className="flex justify-between items-center mt-2">
          <div className="h-4 bg-muted rounded w-1/4"></div>
          <div className="h-4 bg-muted rounded w-1/4"></div>
        </div>
        <div className="mt-4 border border-border rounded-xs px-4 py-4 w-full text-center ">
          <div className="h-4 bg-muted rounded"></div>
        </div>
      </div>
    </div>
  );
};

export default ProductCardLoading;
