#!/usr/bin/env python3
"""
Product Weight Scraper
Scrapes product weights from various e-commerce websites organized by brand.
Processes products in batches based on brand for efficient scraping.
"""

import pandas as pd
import requests
from bs4 import BeautifulSoup
import time
import json
import re
import logging
from urllib.parse import quote_plus, urljoin
from concurrent.futures import ThreadPoolExecutor, as_completed
import random
from dataclasses import dataclass
from typing import List, Dict, Optional, Tuple
import os
from datetime import datetime

# Import configuration
try:
    from config import *
except ImportError:
    print("Warning: config.py not found. Using default settings.")
    # Default configuration if config.py is not available
    CSV_FILE_PATH = "products_export_20250709_161453.csv"
    SCRAPING_CONFIG = {'max_workers': 2, 'batch_size': 10, 'request_timeout': 10}
    USER_AGENTS = ['Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36']
    LOGGING_CONFIG = {'level': 'INFO', 'format': '%(asctime)s - %(levelname)s - %(message)s', 'file': 'scraper.log'}

# Configure logging
logging.basicConfig(
    level=getattr(logging, LOGGING_CONFIG.get('level', 'INFO')),
    format=LOGGING_CONFIG.get('format', '%(asctime)s - %(levelname)s - %(message)s'),
    handlers=[
        logging.FileHandler(LOGGING_CONFIG.get('file', 'scraper.log')),
        logging.StreamHandler() if LOGGING_CONFIG.get('console', True) else logging.NullHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class Product:
    """Product data structure"""
    name: str
    category: str
    subcategory: str
    brand: str
    price: float
    weight: Optional[str] = None
    source_url: Optional[str] = None
    scraped_at: Optional[str] = None

class ProductWeightScraper:
    """Main scraper class for finding product weights"""

    def __init__(self, csv_file_path: str = None):
        self.csv_file_path = csv_file_path or CSV_FILE_PATH
        self.products = []
        self.scraped_data = []
        self.session = requests.Session()

        # Load configuration
        self.config = SCRAPING_CONFIG
        self.user_agents = USER_AGENTS
        self.search_sites = SEARCH_SITES if 'SEARCH_SITES' in globals() else self._get_default_sites()
        self.weight_patterns = WEIGHT_PATTERNS if 'WEIGHT_PATTERNS' in globals() else self._get_default_patterns()
        self.stop_words = STOP_WORDS if 'STOP_WORDS' in globals() else ['the', 'and', 'or', 'with', 'for']

        # Create output directory if specified
        if 'OUTPUT_DIR' in globals() and OUTPUT_DIR:
            os.makedirs(OUTPUT_DIR, exist_ok=True)

    def _get_default_sites(self):
        """Default site configuration if config not available"""
        return {
            'amazon': {
                'url': 'https://www.amazon.in/s?k={query}',
                'product_link_selectors': ['a.s-link-style'],
                'weight_selectors': [
                    '[data-feature-name="detailBullets"] span:contains("Weight")',
                    '.a-section .a-spacing-small span:contains("Weight")',
                    '#feature-bullets ul li span:contains("Weight")',
                    '.a-unordered-list .a-list-item:contains("Weight")'
                ],
                'base_url': 'https://www.amazon.in'
            },
            'flipkart': {
                'url': 'https://www.flipkart.com/search?q={query}',
                'product_link_selectors': ['a._1fQZEK'],
                'weight_selectors': [
                    '._1AN87F:contains("Weight")',
                    '.row:contains("Weight") ._21lJbe',
                    '._1hKmbr:contains("Weight")',
                    '.col:contains("Weight") span'
                ],
                'base_url': 'https://www.flipkart.com'
            }
        }

    def _get_default_patterns(self):
        """Default weight patterns if config not available"""
        return [
            r'(\d+(?:\.\d+)?)\s*(?:kg|kilogram|kilograms)',
            r'(\d+(?:\.\d+)?)\s*(?:g|gram|grams|gm)',
            r'(\d+(?:\.\d+)?)\s*(?:lb|lbs|pound|pounds)',
            r'(\d+(?:\.\d+)?)\s*(?:oz|ounce|ounces)',
            r'weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)'
        ]
    
    def load_products(self) -> None:
        """Load products from CSV file"""
        try:
            df = pd.read_csv(self.csv_file_path)
            logger.info(f"Loaded {len(df)} products from {self.csv_file_path}")
            
            for _, row in df.iterrows():
                if pd.notna(row['Name']) and pd.notna(row['Brand']):
                    product = Product(
                        name=str(row['Name']).strip(),
                        category=str(row['Category']).strip() if pd.notna(row['Category']) else '',
                        subcategory=str(row['Subcategory']).strip() if pd.notna(row['Subcategory']) else '',
                        brand=str(row['Brand']).strip(),
                        price=float(row['Price']) if pd.notna(row['Price']) else 0.0
                    )
                    self.products.append(product)
            
            logger.info(f"Successfully parsed {len(self.products)} valid products")
            
        except Exception as e:
            logger.error(f"Error loading products: {e}")
            raise
    
    def get_random_headers(self) -> Dict[str, str]:
        """Get random headers for requests"""
        return {
            'User-Agent': random.choice(self.user_agents),
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        }
    
    def clean_product_name(self, name: str) -> str:
        """Clean product name for better search results"""
        # Remove special characters and extra spaces
        cleaned = re.sub(r'[^\w\s-]', ' ', name)
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # Remove common words that might interfere with search
        words = cleaned.split()
        filtered_words = [word for word in words if word.lower() not in self.stop_words]

        # Limit query length
        max_length = getattr(self, 'max_query_length', 100)
        result = ' '.join(filtered_words[:8])  # Limit to first 8 words

        if len(result) > max_length:
            result = result[:max_length].rsplit(' ', 1)[0]  # Cut at word boundary

        return result
    
    def extract_weight_from_text(self, text: str) -> Optional[str]:
        """Extract weight information from text"""
        if not text:
            return None

        text_lower = text.lower()
        for pattern in self.weight_patterns:
            matches = re.findall(pattern, text_lower, re.IGNORECASE)
            if matches:
                # Find the unit that was matched
                unit_match = re.search(r'(kg|g|lb|oz|gm|kilogram|gram|pound|ounce)', text_lower)
                if unit_match:
                    return matches[0] + ' ' + unit_match.group()

        return None
    
    def search_product_weight(self, product: Product, site_name: str, site_config: Dict) -> Optional[str]:
        """Search for product weight on a specific site"""
        try:
            # Skip if site is disabled
            if hasattr(self, 'config') and 'enabled_sites' in self.config:
                if not self.config['enabled_sites'].get(site_name, True):
                    return None

            # Create search query
            search_query = f"{product.brand} {self.clean_product_name(product.name)}"
            search_url = site_config['url'].format(query=quote_plus(search_query))

            logger.info(f"Searching {site_name} for: {search_query}")

            # Make request with configurable delay
            delay_min = self.config.get('request_delay_min', 1)
            delay_max = self.config.get('request_delay_max', 3)
            time.sleep(random.uniform(delay_min, delay_max))

            timeout = self.config.get('request_timeout', 10)
            response = self.session.get(search_url, headers=self.get_random_headers(), timeout=timeout)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, 'html.parser')

            # Get product links using configured selectors
            product_links = []
            link_selectors = site_config.get('product_link_selectors', ['a'])
            max_pages = self.config.get('max_product_pages', 3)

            for selector in link_selectors:
                links = soup.select(selector)[:max_pages]
                base_url = site_config.get('base_url', '')
                for link in links:
                    href = link.get('href')
                    if href:
                        if href.startswith('http'):
                            product_links.append(href)
                        else:
                            product_links.append(urljoin(base_url, href))
                if product_links:
                    break  # Use first successful selector

            # Search for weight in product pages
            for product_url in product_links[:max_pages]:
                try:
                    time.sleep(random.uniform(delay_min, delay_max))
                    product_response = self.session.get(product_url, headers=self.get_random_headers(), timeout=timeout)
                    product_soup = BeautifulSoup(product_response.content, 'html.parser')

                    # Extract weight using selectors
                    for selector in site_config['weight_selectors']:
                        elements = product_soup.select(selector)
                        for element in elements:
                            weight = self.extract_weight_from_text(element.get_text())
                            if weight:
                                logger.info(f"Found weight for {product.name}: {weight}")
                                return weight

                    # Also search in all text content
                    page_text = product_soup.get_text()
                    weight = self.extract_weight_from_text(page_text)
                    if weight:
                        logger.info(f"Found weight in page text for {product.name}: {weight}")
                        return weight

                except Exception as e:
                    logger.warning(f"Error processing product page {product_url}: {e}")
                    continue

            return None

        except Exception as e:
            logger.error(f"Error searching {site_name} for {product.name}: {e}")
            return None
    
    def scrape_product_weight(self, product: Product) -> Product:
        """Scrape weight for a single product"""
        logger.info(f"Scraping weight for: {product.name} ({product.brand})")
        
        # Try each site until we find weight information
        for site_name, site_config in self.search_sites.items():
            try:
                weight = self.search_product_weight(product, site_name, site_config)
                if weight:
                    product.weight = weight
                    product.source_url = site_name
                    product.scraped_at = datetime.now().isoformat()
                    logger.info(f"Successfully found weight for {product.name}: {weight}")
                    return product
            except Exception as e:
                logger.error(f"Error with {site_name} for {product.name}: {e}")
                continue
        
        logger.warning(f"No weight found for: {product.name}")
        product.scraped_at = datetime.now().isoformat()
        return product
    
    def process_brand_batch(self, brand: str, max_workers: int = 3) -> List[Product]:
        """Process all products for a specific brand"""
        brand_products = [p for p in self.products if p.brand.lower() == brand.lower()]
        logger.info(f"Processing {len(brand_products)} products for brand: {brand}")
        
        results = []
        
        # Process in smaller batches to avoid overwhelming servers
        batch_size = 10
        for i in range(0, len(brand_products), batch_size):
            batch = brand_products[i:i + batch_size]
            logger.info(f"Processing batch {i//batch_size + 1} for {brand} ({len(batch)} products)")
            
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_product = {executor.submit(self.scrape_product_weight, product): product 
                                   for product in batch}
                
                for future in as_completed(future_to_product):
                    try:
                        result = future.result()
                        results.append(result)
                    except Exception as e:
                        product = future_to_product[future]
                        logger.error(f"Error processing {product.name}: {e}")
                        product.scraped_at = datetime.now().isoformat()
                        results.append(product)
            
            # Delay between batches
            time.sleep(random.uniform(5, 10))
        
        return results
    
    def save_results(self, filename: str = None) -> None:
        """Save results to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"product_weights_{timestamp}.json"
        
        # Convert to dictionary format
        results_dict = {
            'scraping_info': {
                'total_products': len(self.scraped_data),
                'products_with_weight': len([p for p in self.scraped_data if p.weight]),
                'scraped_at': datetime.now().isoformat(),
                'brands_processed': list(set(p.brand for p in self.scraped_data))
            },
            'products_by_brand': {}
        }
        
        # Group by brand
        for product in self.scraped_data:
            brand = product.brand
            if brand not in results_dict['products_by_brand']:
                results_dict['products_by_brand'][brand] = []
            
            product_dict = {
                'name': product.name,
                'category': product.category,
                'subcategory': product.subcategory,
                'brand': product.brand,
                'price': product.price,
                'weight': product.weight,
                'source_url': product.source_url,
                'scraped_at': product.scraped_at
            }
            results_dict['products_by_brand'][brand].append(product_dict)
        
        # Save to file
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results_dict, f, indent=2, ensure_ascii=False)
        
        logger.info(f"Results saved to {filename}")
        
        # Also save summary
        summary_filename = filename.replace('.json', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("PRODUCT WEIGHT SCRAPING SUMMARY\n")
            f.write("=" * 50 + "\n\n")
            f.write(f"Total products processed: {len(self.scraped_data)}\n")
            f.write(f"Products with weight found: {len([p for p in self.scraped_data if p.weight])}\n")
            f.write(f"Success rate: {len([p for p in self.scraped_data if p.weight])/len(self.scraped_data)*100:.1f}%\n\n")
            
            for brand in results_dict['products_by_brand']:
                brand_products = results_dict['products_by_brand'][brand]
                with_weight = len([p for p in brand_products if p['weight']])
                f.write(f"{brand}: {with_weight}/{len(brand_products)} products with weight\n")
        
        logger.info(f"Summary saved to {summary_filename}")

def main():
    """Main function to run the scraper"""
    # Get CSV file path from config or use default
    csv_file = CSV_FILE_PATH if 'CSV_FILE_PATH' in globals() else "products_export_20250709_161453.csv"

    if not os.path.exists(csv_file):
        logger.error(f"CSV file not found: {csv_file}")
        return

    # Initialize scraper
    scraper = ProductWeightScraper(csv_file)
    
    try:
        # Load products
        scraper.load_products()
        
        # Get unique brands
        brands = list(set(product.brand for product in scraper.products))
        logger.info(f"Found brands: {brands}")

        # Sort brands by priority if configured
        if 'BRAND_PRIORITY' in globals() and BRAND_PRIORITY:
            priority_brands = [b for b in BRAND_PRIORITY if b in brands]
            other_brands = [b for b in brands if b not in BRAND_PRIORITY]
            brands = priority_brands + other_brands

        # Process each brand in batches
        for brand in brands:
            logger.info(f"\n{'='*50}")
            logger.info(f"PROCESSING BRAND: {brand}")
            logger.info(f"{'='*50}")
            
            # Get max_workers from config
            max_workers = scraper.config.get('max_workers', 2)
            brand_results = scraper.process_brand_batch(brand, max_workers=max_workers)
            scraper.scraped_data.extend(brand_results)

            # Save intermediate results if enabled
            if OUTPUT_CONFIG.get('save_intermediate', True):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_dir = OUTPUT_DIR if 'OUTPUT_DIR' in globals() and OUTPUT_DIR else ""
                intermediate_file = os.path.join(output_dir, f"weights_{brand.lower()}_{timestamp}.json")
            
            # Save brand-specific results
            brand_dict = {
                'brand': brand,
                'products': [
                    {
                        'name': p.name,
                        'category': p.category,
                        'subcategory': p.subcategory,
                        'brand': p.brand,
                        'price': p.price,
                        'weight': p.weight,
                        'source_url': p.source_url,
                        'scraped_at': p.scraped_at
                    } for p in brand_results
                ]
            }
            
            with open(intermediate_file, 'w', encoding='utf-8') as f:
                json.dump(brand_dict, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Intermediate results for {brand} saved to {intermediate_file}")
            
            # Configurable delay between brands
            brand_delay_min = scraper.config.get('brand_delay_min', 10)
            brand_delay_max = scraper.config.get('brand_delay_max', 20)
            time.sleep(random.uniform(brand_delay_min, brand_delay_max))
        
        # Save final results
        scraper.save_results()
        
        logger.info("\n" + "="*50)
        logger.info("SCRAPING COMPLETED SUCCESSFULLY!")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"Error in main execution: {e}")
        raise

if __name__ == "__main__":
    main()
