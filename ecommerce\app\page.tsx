"use client";
import React, { Suspense } from "react";
import { motion, useScroll, useTransform, useSpring } from "framer-motion";
import { useToast } from "../components/ui/use-toast";
import {
  CATEGORIES,
  CATEGORIZE_PRODUCTS,
  FUTURED_PRODUCTS,
  MAIN_URL,
  PRODUCTS,
} from "../constant/urls";
import useApi from "../hooks/useApi";
import MainHOF from "../layout/MainHOF";
import { useCallback, useEffect, useState, useMemo, useRef } from "react";
import TrustIndicators from "@/components/ui/TrustIndicators";
import ScrollToTop from "@/components/ui/ScrollToTop";
import ClientOnly from "@/components/ClientOnly";
import "../styles/scroll-animations.css";

// Lazy load components outside of the component function
const HeroCarousel = React.lazy(() => import('@/components/home/<USER>'));
const ProductSection = React.lazy(() => import('@/components/home/<USER>'));
const CategoryTabs = React.lazy(() => import('@/components/home/<USER>'));
const ProductCategories = React.lazy(() => import('@/components/home/<USER>'));

// Define types
interface CategoryProducts {
  category: any;
  products: any[];
  loading: boolean;
}
const Homepage = () => {
  const { toast } = useToast();
  // Use a single API instance for all API calls
  const { data, read }: any = useApi(MAIN_URL || '');

  // Create state variables to store different data types
  const [futureProduct, setFutureProduct] = useState<any>(null);
  const [futureProductLoading, setFutureProductLoading] = useState<boolean>(false);
  const [popularProduct, setPopularProduct] = useState<any>(null);
  const [popularProductLoading, setPopularProductLoading] = useState<boolean>(false);
  const [categories, setCategories] = useState<any[]>([]);
  const [categoriesLoading, setCategoriesLoading] = useState<boolean>(false);

  // Use refs to track if data has been fetched
  const initialDataFetchedRef = useRef<boolean>(false);

  // Scroll animation setup
  const containerRef = useRef<HTMLDivElement>(null);
  const { scrollYProgress } = useScroll({
    target: containerRef,
    offset: ["start start", "end start"]
  });

  // Smooth spring animation for scroll progress
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Parallax transforms for background elements
  const backgroundY = useTransform(smoothProgress, [0, 1], ["0%", "50%"]);
  const backgroundScale = useTransform(smoothProgress, [0, 1], [1, 1.1]);
  const backgroundOpacity = useTransform(smoothProgress, [0, 0.5, 1], [0.7, 0.4, 0.1]);

  const [categoryProducts, setCategoryProducts] = useState<CategoryProducts[]>([]);

  // Track if category products have been fetched
  const categoryProductsFetchedRef = useRef<boolean>(false);

  // Function to fetch products for each category - optimized to prevent continuous API calls
  const fetchCategoryProducts = useCallback(async () => {
    // Skip if categories aren't loaded yet
    if (!Array.isArray(categories) || categories.length === 0) return;

    // Skip if we've already fetched and have data
    if (categoryProductsFetchedRef.current &&
        categoryProducts.length > 0 &&
        categoryProducts.every(cat => !cat.loading)) {
      return;
    }

    // Limit to first 6 categories to avoid overwhelming the page and reduce API calls
    const limitedCategories = categories.slice(0, 6);

    // Set initial loading state
    if (categoryProducts.length === 0) {
      const initialCategoryProducts = limitedCategories.map((category: any) => ({
        category,
        products: [],
        loading: true,
      }));
      setCategoryProducts(initialCategoryProducts);
    }

    // Fetch products for each category
    const promises = limitedCategories.map(async (category, index) => {
      try {
        // Use the single read function from our consolidated API instance
        const result = await read(
          `${CATEGORIZE_PRODUCTS(category.slug)}?page_size=8`
        );

        return {
          index,
          category,
          products: result?.results?.products || [],
          success: true
        };
      } catch (error) {
        console.error(`Error fetching products for ${category.name}:`, error);
        return {
          index,
          category,
          products: [],
          success: false
        };
      }
    });

    // Wait for all promises to resolve
    const results = await Promise.all(promises);

    // Update state once with all results
    setCategoryProducts(prev => {
      // Start with previous state or empty array
      const newState = prev.length > 0 ? [...prev] :
        limitedCategories.map(cat => ({ category: cat, products: [], loading: true }));

      // Update with new results
      results.forEach(result => {
        if (newState[result.index]) {
          newState[result.index] = {
            ...newState[result.index],
            products: result.products,
            loading: false,
          };
        }
      });

      // Mark as fetched
      categoryProductsFetchedRef.current = true;

      return newState;
    });
  }, [categories, read]);

  // Load all initial data with a single useEffect to reduce renders and prevent continuous API calls
  useEffect(() => {
    // Skip if we've already loaded the data
    if (initialDataFetchedRef.current) return;

    // Create a flag to track if the component is still mounted
    let isMounted = true;

    const loadInitialData = async () => {
      try {
        setCategoriesLoading(true);
        setFutureProductLoading(true);
        setPopularProductLoading(true);

        // Load categories first
        const categoriesResult = await read(CATEGORIES);

        // Only continue if component is still mounted
        if (!isMounted) return;

        // Update categories state
        if (categoriesResult) {
          setCategories(categoriesResult);
        }

        setCategoriesLoading(false);

        // Load featured and popular products in parallel
        const [featuredResult, popularResult] = await Promise.all([
          read(FUTURED_PRODUCTS),
          read(PRODUCTS + '?page_size=10') // Fetch 10 products for the Discover section
        ]);

        if (!isMounted) return;

        // Update featured products state
        if (featuredResult) {
          setFutureProduct(featuredResult);
        }

        // Update popular products state
        if (popularResult) {
          setPopularProduct(popularResult);
        }

        // Mark as fetched to prevent duplicate API calls
        initialDataFetchedRef.current = true;
        setFutureProductLoading(false);
        setPopularProductLoading(false);
      } catch (error) {
        console.error("Error loading initial data:", error);
        setCategoriesLoading(false);
        setFutureProductLoading(false);
        setPopularProductLoading(false);
      }
    };

    loadInitialData();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, [read]);

  // Fetch products for each category when categories are loaded
  // This useEffect now depends only on categories and fetchCategoryProducts
  // It will only run when categories change, not on every render
  useEffect(() => {
    if (categories && categories.length > 0 && !categoryProductsFetchedRef.current) {
      fetchCategoryProducts();
    }
  }, [fetchCategoryProducts, categories]);


  // We no longer need featuredProductsForHero since we're using HeroCarousel

  // Get featured products for product section - memoized to prevent recalculations
  const featuredProducts = useMemo(() => {
    if (futureProduct && Array.isArray(futureProduct)) {
      return futureProduct;
    } else if (futureProduct && futureProduct.results && Array.isArray(futureProduct.results)) {
      return futureProduct.results;
    } else if (futureProduct && futureProduct.products && Array.isArray(futureProduct.products)) {
      return futureProduct.products;
    }
    // Return empty array if no products are available - no more static fallback
    return [];
  }, [futureProduct]);

  // Get popular products - memoized to prevent recalculations
  const popularProducts = useMemo(() => {
    // First check our dedicated popularProduct state
    if (popularProduct && Array.isArray(popularProduct)) {
      return popularProduct;
    } else if (popularProduct && popularProduct.results && Array.isArray(popularProduct.results)) {
      return popularProduct.results;
    } else if (popularProduct && popularProduct.products && Array.isArray(popularProduct.products)) {
      return popularProduct.products;
    }

    // Fallback to data from the main API call if popularProduct isn't available
    if (data && Array.isArray(data)) {
      return data;
    } else if (data && data.results && Array.isArray(data.results)) {
      return data.results;
    } else if (data && data.products && Array.isArray(data.products)) {
      return data.products;
    }

    // Return empty array if no products are available - no more static fallback
    return [];
  }, [popularProduct, data]);

  return (
    <div ref={containerRef} className="bg-theme-homepage w-full relative overflow-hidden scroll-snap-container">
      {/* Scroll Progress Indicator */}
      <motion.div
        className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary z-50 origin-left"
        style={{ scaleX: smoothProgress }}
      />

      {/* Enhanced Background decorative elements with parallax */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <motion.div
          className="absolute top-0 left-0 w-full h-[800px] bg-gradient-to-b from-theme-accent-primary/5 to-transparent"
          style={{
            opacity: backgroundOpacity,
            scale: backgroundScale
          }}
        />
        <motion.div
          className="absolute top-[10%] right-[-10%] w-[500px] h-[500px] rounded-full bg-gradient-to-br from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl"
          style={{
            y: backgroundY,
            scale: backgroundScale,
            opacity: backgroundOpacity
          }}
        />
        <motion.div
          className="absolute top-[40%] left-[-10%] w-[600px] h-[600px] rounded-full bg-gradient-to-tr from-theme-accent-primary/5 to-theme-accent-secondary/10 blur-3xl"
          style={{
            y: useTransform(smoothProgress, [0, 1], ["0%", "-30%"]),
            scale: backgroundScale,
            opacity: backgroundOpacity
          }}
        />
        <motion.div
          className="absolute bottom-[5%] right-[5%] w-[400px] h-[400px] rounded-full bg-gradient-to-bl from-theme-accent-secondary/10 to-theme-accent-primary/5 blur-3xl"
          style={{
            y: useTransform(smoothProgress, [0, 1], ["0%", "20%"]),
            scale: backgroundScale,
            opacity: backgroundOpacity
          }}
        />
      </div>

      {/* <PromoBanner /> */}
      <MainHOF>
        <div className="min-h-screen flex flex-col w-full relative z-10 scroll-snap-container">
          {/* Navigation - Amazon/Flipkart style category navigation */}
          <motion.div
            className="relative z-20"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          >
            <ProductCategories
              categories={categories ?? []}
              variant="navigation"
              showTitle={false}
              showViewAll={false}
              maxCategories={12}
              accentColor="primary"
            />
          </motion.div>

          {/* Use a single Suspense boundary for all lazy-loaded components */}
          <ClientOnly>
            <React.Suspense fallback={
              <div className="w-full space-y-8">
                <div className="w-full h-[400px] bg-gray-100 animate-pulse rounded-xl"></div>
                <div className="w-full h-[300px] bg-gray-100 animate-pulse rounded-xl"></div>
                <div className="w-full h-[300px] bg-gray-100 animate-pulse rounded-xl"></div>
              </div>
            }>
              {/* Hero Section with Featured Products */}
              <motion.section
                className="relative w-full scroll-snap-section"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
              >
                <HeroCarousel />
                <motion.div
                  className="relative z-10 mt-6 mb-8"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, ease: "easeOut", delay: 0.5 }}
                >
                  <TrustIndicators />
                </motion.div>
              </motion.section>

            {/* Featured Products Section */}
            <motion.div
              className="scroll-snap-section"
              initial={{ opacity: 0, y: 60 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8, ease: "easeOut" }}
            >
              <ProductSection
                title="Featured Products"
                subtitle="Discover our handpicked selection of premium products"
                products={featuredProducts}
                loading={futureProductLoading}
                viewAllLink="/shop"
                accentColor="primary"
                columns={{
                  xs: 2,
                  sm: 2,
                  md: 3,
                  lg: 4,
                  xl: 4,
                  "2xl": 5
                }}
              />
            </motion.div>

            {/* Popular Products Section */}
            <motion.div
              initial={{ opacity: 0, x: -60 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true, margin: "-100px" }}
              transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
            >
              <ProductSection
                title="Discover Products"
                subtitle="Explore our most popular items"
                products={popularProducts}
                loading={popularProductLoading}
                viewAllLink="/shop"
                accentColor="secondary"
                columns={{
                  xs: 2,
                  sm: 2,
                  md: 3,
                  lg: 4,
                  xl: 4,
                  "2xl": 5
                }}
              />
            </motion.div>

            {/* Product Categories Grid - Always render with fallback categories */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              whileInView={{ opacity: 1, scale: 1 }}
              viewport={{ once: true, margin: "-50px" }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <ProductCategories
                title="Shop by Category"
                subtitle="Browse our collection by category"
                categories={categories || []}
                accentColor="tertiary"
              />
            </motion.div>

            {/* Category Tabs Section - Only render categories with products */}
            {categoryProducts.some(cat => cat.products && cat.products.length > 0) && (
              <motion.div
                initial={{ opacity: 0, y: 80 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true, margin: "-100px" }}
                transition={{ duration: 0.9, ease: "easeOut", delay: 0.1 }}
              >
                <CategoryTabs
                  title="Browse Products by Category"
                  subtitle="Filter products by your favorite categories"
                  categories={categories || []}
                  categoryProducts={categoryProducts.filter(cat => cat.products && cat.products.length > 0)}
                  accentColor="primary"
                />
              </motion.div>
            )}

            {/* Individual Category Sections */}
            {categoryProducts.map((categoryData, categoryIndex) => (
              // Only show categories with at least 4 products
              Array.isArray(categoryData.products) && categoryData.products.length >= 4 ? (
                <motion.div
                  key={categoryData.category.id}
                  initial={{ opacity: 0, x: categoryIndex % 2 === 0 ? -60 : 60 }}
                  whileInView={{ opacity: 1, x: 0 }}
                  viewport={{ once: true, margin: "-80px" }}
                  transition={{
                    duration: 0.8,
                    ease: "easeOut",
                    delay: categoryIndex * 0.1
                  }}
                >
                  <ProductSection
                    title={categoryData.category.name}
                    products={categoryData.products}
                    loading={categoryData.loading}
                    viewAllLink={`/shop?category=${categoryData.category.slug}`}
                    accentColor={categoryIndex % 3 === 0 ? "primary" : categoryIndex % 3 === 1 ? "secondary" : "tertiary"}
                    columns={{
                      xs: 2,
                      sm: 2,
                      md: 3,
                      lg: 4,
                      xl: 4,
                      "2xl": 5
                    }}
                  />
                </motion.div>
              ) : null
            ))}
            </React.Suspense>
          </ClientOnly>
        </div>
      </MainHOF>

      {/* Scroll to Top Button */}
      <ScrollToTop />
    </div>
  );
};

export default Homepage;
