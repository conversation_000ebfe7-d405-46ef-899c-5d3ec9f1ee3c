"use client";

import React, { useState, useEffect, useRef } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import Product from "../product/Product";
import ProductCardLoading from "../ui/loading/ProductCardLoading";

interface CategoryTabsProps {
  categories: any[];
  categoryProducts: {
    category: any;
    products: any[];
    loading: boolean;
  }[];
  title: string;
  subtitle?: string;
  accentColor?: "primary" | "secondary" | "tertiary";
}

const CategoryTabs: React.FC<CategoryTabsProps> = ({
  categories,
  categoryProducts,
  title,
  subtitle,
  accentColor = "primary",
}) => {
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [visibleCategories, setVisibleCategories] = useState<any[]>([]);

  // Default fallback products if needed - using real product images instead of placeholders
  const fallbackProducts = [
    {
      id: 1,
      name: "Smart Door Lock",
      price: 199.99,
      discount_price: 149.99,
      discount_percentage: 25,
      description: "Advanced security with fingerprint and PIN access",
      image: "/assets/products/smart-door-lock.svg", // Use local SVG image instead of placeholder
      slug: "smart-door-lock",
      category: { name: "Security", slug: "security" }
    },
    {
      id: 2,
      name: "Digital Safe",
      price: 299.99,
      discount_price: 249.99,
      discount_percentage: 16,
      description: "Secure storage for valuables with digital access",
      image: "/assets/products/digital-safe.svg", // Use local SVG image instead of placeholder
      slug: "digital-safe",
      category: { name: "Security", slug: "security" }
    },
    {
      id: 3,
      name: "Smart Camera",
      price: 129.99,
      discount_price: 99.99,
      discount_percentage: 23,
      description: "HD security camera with motion detection",
      image: "/assets/products/smart-camera.svg", // Use local SVG image instead of placeholder
      slug: "smart-camera",
      category: { name: "Security", slug: "security" }
    },
    {
      id: 4,
      name: "Video Doorbell",
      price: 149.99,
      discount_price: 129.99,
      discount_percentage: 13,
      description: "See who's at your door from anywhere",
      image: "/assets/products/video-doorbell.svg", // Use local SVG image instead of placeholder
      slug: "video-doorbell",
      category: { name: "Security", slug: "security" }
    }
  ];

  // Track if we've already set the initial active tab to prevent continuous state updates
  const initialTabSetRef = useRef<boolean>(false);

  // Set initial active tab when data is loaded - optimized to prevent continuous updates
  useEffect(() => {
    // Only work with categories that have products
    const effectiveCategoryProducts = categoryProducts.filter(cat =>
      cat.products && cat.products.length > 0
    );

    // If we don't have any category products with actual products, don't show anything
    if (effectiveCategoryProducts.length === 0) {
      setVisibleCategories([]);
      return;
    }

    // Set initial active tab only once
    if (!activeTab && !initialTabSetRef.current && effectiveCategoryProducts.length > 0) {
      // Use the first category with products
      setActiveTab(effectiveCategoryProducts[0].category.slug);
      initialTabSetRef.current = true;
    }

    // Extract just the category objects from categories with products
    const categoriesWithProducts = effectiveCategoryProducts.map(cat => cat.category);

    // Only update state if the visible categories have changed
    if (JSON.stringify(categoriesWithProducts) !== JSON.stringify(visibleCategories)) {
      setVisibleCategories(categoriesWithProducts);
    }
  }, [categoryProducts, activeTab, visibleCategories]);

  // Get current active category products - only from categories with products
  const activeCategory = categoryProducts.find(
    (cat) => cat.category.slug === activeTab && cat.products && cat.products.length > 0
  );

  // Set the active category - only if it has products
  const effectiveActiveCategory = activeCategory || (visibleCategories.length > 0 ? {
    category: visibleCategories[0],
    products: categoryProducts.find(cat => cat.category.slug === visibleCategories[0].slug)?.products || [],
    loading: false
  } : null);

  // Determine accent color classes
  const accentClasses = {
    primary: {
      bg: "bg-theme-accent-primary/20",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/5",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
    secondary: {
      bg: "bg-theme-accent-secondary/30",
      text: "text-theme-accent-secondary",
      line: "bg-theme-accent-secondary",
      gradient: "from-theme-accent-secondary/5",
      activeBg: "bg-theme-accent-secondary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-secondary/10",
    },
    tertiary: {
      bg: "bg-theme-accent-primary/30",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/10",
      activeBg: "bg-theme-accent-primary",
      activeText: "text-white",
      hoverBg: "hover:bg-theme-accent-primary/10",
    },
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  // Only render the component if there are categories with products
  if (visibleCategories.length === 0) {
    return null;
  }

  return (
    <section className="py-8 sm:py-12 md:py-16 relative overflow-hidden w-full">
      {/* Background gradient */}
      <div className={`absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`}></div>

      {/* Decorative elements */}
      <div className="absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50"></div>
      <div className="absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]">
        {/* Section header */}
        <div className="flex flex-col items-center mb-6 sm:mb-8 md:mb-10">
          <h2 className="text-xl sm:text-2xl md:text-3xl font-bold text-theme-text-primary mb-2 sm:mb-3 relative text-center px-2">
            <span className="relative z-10">{title}</span>
            <span className={`absolute -bottom-1 left-0 right-0 h-2 sm:h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`}></span>
          </h2>
          {subtitle && <p className="text-theme-text-primary/70 text-center text-sm sm:text-base max-w-2xl mb-3 px-4">{subtitle}</p>}
          <div className={`w-12 sm:w-16 md:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`}></div>
        </div>

        {/* Category tabs - only show if we have categories with products */}
        {visibleCategories.length > 0 && (
          <div className="w-full mb-8 relative">
            {/* Full width scrollable container with padding to ensure visibility */}
            <div className="w-full overflow-x-auto pb-4 scrollbar-hide -mx-4 px-4">
              <div className="flex space-x-3 min-w-max">
                {visibleCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveTab(category.slug)}
                    className={`px-3 sm:px-4 py-2 rounded-full text-sm sm:text-base font-medium transition-all duration-300 whitespace-nowrap flex-shrink-0
                      ${
                        activeTab === category.slug
                          ? `${accentClasses[accentColor].activeBg} ${accentClasses[accentColor].activeText}`
                          : `bg-gray-100 text-gray-700 ${accentClasses[accentColor].hoverBg}`
                      }
                    `}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
            {/* Fade indicators for scrollable content */}
            <div className="absolute left-0 top-0 bottom-4 w-8 bg-gradient-to-r from-theme-homepage to-transparent pointer-events-none"></div>
            <div className="absolute right-0 top-0 bottom-4 w-8 bg-gradient-to-l from-theme-homepage to-transparent pointer-events-none"></div>
          </div>
        )}

        {/* Products grid */}
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
          >
            {effectiveActiveCategory && effectiveActiveCategory.products && effectiveActiveCategory.products.length > 0 ? (
              <>
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="show"
                  className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4 2xl:grid-cols-5 gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6"
                >
                  {effectiveActiveCategory.loading &&
                    Array.from({ length: 8 }).map((_, index) => (
                      <motion.div
                        key={index}
                        variants={itemVariants}
                        className="transform transition-transform duration-300 hover:scale-[1.02] hover:-translate-y-1"
                      >
                        <ProductCardLoading />
                      </motion.div>
                    ))}

                  {!effectiveActiveCategory.loading &&
                    effectiveActiveCategory.products.map((product) => (
                      <motion.div
                        key={product.id || `fallback-product-${Math.random()}`}
                        variants={itemVariants}
                        className="transform transition-transform duration-300 hover:scale-[1.02] hover:-translate-y-1"
                      >
                        <Product {...product} />
                      </motion.div>
                    ))}
                </motion.div>

                {/* View all link */}
                <div className="flex justify-center mt-6 sm:mt-8">
                  <Link
                    href={`/shop?category=${effectiveActiveCategory.category.slug}`}
                    className={`flex items-center px-4 py-2 rounded-full ${accentClasses[accentColor].text} hover:text-theme-accent-hover hover:bg-gray-100 group transition-all duration-300`}
                  >
                    <span className="font-medium">View All {effectiveActiveCategory.category.name}</span>
                    <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
                  </Link>
                </div>
              </>
            ) : (
              <div className="text-center py-12">
                <p className="text-gray-500">No products found in this category</p>
              </div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </section>
  );
};

export default CategoryTabs;
