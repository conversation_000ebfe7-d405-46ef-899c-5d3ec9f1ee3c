"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_home_ProductSection_tsx";
exports.ids = ["_ssr_components_home_ProductSection_tsx"];
exports.modules = {

/***/ "(ssr)/./components/home/<USER>":
/*!********************************************!*\
  !*** ./components/home/<USER>
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _product_Product__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../product/Product */ \"(ssr)/./components/product/Product.tsx\");\n/* harmony import */ var _ui_loading_ProductCardLoading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../ui/loading/ProductCardLoading */ \"(ssr)/./components/ui/loading/ProductCardLoading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n// Use React.memo to prevent unnecessary re-renders\nconst ProductSection = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1___default().memo(({ title, subtitle, products, loading, viewAllLink, accentColor = \"primary\", layout = \"grid\", columns = {\n    xs: 2,\n    sm: 2,\n    md: 3,\n    lg: 4,\n    xl: 4,\n    \"2xl\": 5\n} })=>{\n    // Determine accent color classes\n    const accentClasses = {\n        primary: {\n            bg: \"bg-theme-accent-primary/20\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/5\"\n        },\n        secondary: {\n            bg: \"bg-theme-accent-secondary/30\",\n            text: \"text-theme-accent-secondary\",\n            line: \"bg-theme-accent-secondary\",\n            gradient: \"from-theme-accent-secondary/5\"\n        },\n        tertiary: {\n            bg: \"bg-theme-accent-primary/30\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/10\"\n        }\n    };\n    // Generate grid columns classes\n    const getGridColumns = ()=>{\n        const colClasses = [];\n        if (columns.xs) colClasses.push(`xs:grid-cols-${columns.xs}`);\n        if (columns.sm) colClasses.push(`sm:grid-cols-${columns.sm}`);\n        if (columns.md) colClasses.push(`md:grid-cols-${columns.md}`);\n        if (columns.lg) colClasses.push(`lg:grid-cols-${columns.lg}`);\n        if (columns.xl) colClasses.push(`xl:grid-cols-${columns.xl}`);\n        if (columns[\"2xl\"]) colClasses.push(`2xl:grid-cols-${columns[\"2xl\"]}`);\n        return colClasses.join(\" \");\n    };\n    // Enhanced animation variants for staggered children\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.08,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30,\n            scale: 0.9,\n            rotateX: -15\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            rotateX: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\",\n                type: \"spring\",\n                stiffness: 100,\n                damping: 15\n            }\n        }\n    };\n    // Hover animation variants\n    const hoverVariants = {\n        hover: {\n            scale: 1.03,\n            y: -8,\n            rotateY: 2,\n            transition: {\n                duration: 0.3,\n                ease: \"easeOut\"\n            }\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 sm:py-16 md:py-20 relative overflow-hidden w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/4 right-[10%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                lineNumber: 133,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/4 left-[5%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                lineNumber: 134,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-theme-text-primary mb-3 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                        lineNumber: 140,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                lineNumber: 139,\n                                columnNumber: 11\n                            }, undefined),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-theme-text-primary/70 text-center max-w-2xl mb-4\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                lineNumber: 143,\n                                columnNumber: 24\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 9\n                    }, undefined),\n                    viewAllLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: viewAllLink,\n                            className: `flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 154,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 155,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                        lineNumber: 149,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"show\",\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        className: `grid grid-cols-2 ${getGridColumns()} gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6`,\n                        children: [\n                            loading && Array.from({\n                                length: 8\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    variants: itemVariants,\n                                    whileHover: \"hover\",\n                                    className: \"cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ui_loading_ProductCardLoading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                        lineNumber: 176,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, index, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 170,\n                                    columnNumber: 15\n                                }, undefined)),\n                            !loading && products && products.length > 0 ? products.map((product, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                    variants: itemVariants,\n                                    whileHover: {\n                                        scale: 1.03,\n                                        y: -8,\n                                        rotateY: index % 2 === 0 ? 2 : -2,\n                                        transition: {\n                                            duration: 0.3,\n                                            ease: \"easeOut\"\n                                        }\n                                    },\n                                    whileTap: {\n                                        scale: 0.98\n                                    },\n                                    className: \"cursor-pointer\",\n                                    style: {\n                                        transformStyle: \"preserve-3d\",\n                                        perspective: \"1000px\"\n                                    },\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_Product__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        ...product\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, product.id, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 15\n                                }, undefined)) : !loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                                className: \"col-span-full text-center py-8\",\n                                initial: {\n                                    opacity: 0\n                                },\n                                animate: {\n                                    opacity: 1\n                                },\n                                transition: {\n                                    delay: 0.5\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500\",\n                                    children: \"No products found\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 212,\n                                    columnNumber: 17\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                lineNumber: 206,\n                                columnNumber: 15\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                        lineNumber: 161,\n                        columnNumber: 9\n                    }, undefined),\n                    viewAllLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-8 sm:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: viewAllLink,\n                            className: `flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"View All\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 225,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                                    lineNumber: 226,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductSection.tsx\",\n        lineNumber: 128,\n        columnNumber: 5\n    }, undefined);\n});\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductSection);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/home/<USER>");

/***/ })

};
;