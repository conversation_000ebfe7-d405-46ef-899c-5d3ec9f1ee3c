"use client";

import React from "react";
import { motion } from "framer-motion";
import { ChevronRight } from "lucide-react";
import Link from "next/link";
import Product from "../product/Product";
import ProductCardLoading from "../ui/loading/ProductCardLoading";

interface ProductSectionProps {
  title: string;
  subtitle?: string;
  products: any[];
  loading: boolean;
  viewAllLink?: string;
  accentColor?: "primary" | "secondary" | "tertiary";
  layout?: "grid" | "carousel";
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
    "2xl"?: number;
  };
}

// Use React.memo to prevent unnecessary re-renders
const ProductSection: React.FC<ProductSectionProps> = React.memo(({
  title,
  subtitle,
  products,
  loading,
  viewAllLink,
  accentColor = "primary",
  layout = "grid",
  columns = {
    xs: 2,
    sm: 2,
    md: 3,
    lg: 4,
    xl: 4,
    "2xl": 5,
  },
}) => {
  // Determine accent color classes
  const accentClasses = {
    primary: {
      bg: "bg-theme-accent-primary/20",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/5",
    },
    secondary: {
      bg: "bg-theme-accent-secondary/30",
      text: "text-theme-accent-secondary",
      line: "bg-theme-accent-secondary",
      gradient: "from-theme-accent-secondary/5",
    },
    tertiary: {
      bg: "bg-theme-accent-primary/30",
      text: "text-theme-accent-primary",
      line: "bg-theme-accent-primary",
      gradient: "from-theme-accent-primary/10",
    },
  };

  // Generate grid columns classes
  const getGridColumns = () => {
    const colClasses = [];
    if (columns.xs) colClasses.push(`xs:grid-cols-${columns.xs}`);
    if (columns.sm) colClasses.push(`sm:grid-cols-${columns.sm}`);
    if (columns.md) colClasses.push(`md:grid-cols-${columns.md}`);
    if (columns.lg) colClasses.push(`lg:grid-cols-${columns.lg}`);
    if (columns.xl) colClasses.push(`xl:grid-cols-${columns.xl}`);
    if (columns["2xl"]) colClasses.push(`2xl:grid-cols-${columns["2xl"]}`);
    return colClasses.join(" ");
  };

  // Animation variants for staggered children
  const containerVariants = {
    hidden: { opacity: 0 },
    show: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    show: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  };

  return (
    <section className="py-12 sm:py-16 md:py-20 relative overflow-hidden w-full">
      {/* Background gradient */}
      <div className={`absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`}></div>

      {/* Decorative elements */}
      <div className="absolute top-1/4 right-[10%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50"></div>
      <div className="absolute bottom-1/4 left-[5%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50"></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]">
        {/* Section header */}
        <div className="flex flex-col items-center mb-8 sm:mb-12">
          <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-theme-text-primary mb-3 relative">
            <span className="relative z-10">{title}</span>
            <span className={`absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`}></span>
          </h2>
          {subtitle && <p className="text-theme-text-primary/70 text-center max-w-2xl mb-4">{subtitle}</p>}
          <div className={`w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`}></div>
        </div>

        {/* View all link - top position */}
        {viewAllLink && (
          <div className="flex justify-center mb-6">
            <Link
              href={viewAllLink}
              className={`flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`}
            >
              <span>View All</span>
              <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        )}

        {/* Products grid */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
          className={`grid grid-cols-2 ${getGridColumns()} gap-2 xs:gap-3 sm:gap-4 md:gap-5 lg:gap-6`}
        >
          {loading &&
            Array.from({ length: 8 }).map((_, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="transform transition-transform duration-300 hover:scale-[1.02] hover:-translate-y-1"
              >
                <ProductCardLoading />
              </motion.div>
            ))}

          {!loading && products && products.length > 0 ? (
            products.map((product) => (
              <motion.div
                key={product.id}
                variants={itemVariants}
                className="transform transition-transform duration-300 hover:scale-[1.02] hover:-translate-y-1"
              >
                <Product {...product} />
              </motion.div>
            ))
          ) : (
            !loading && (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No products found</p>
              </div>
            )
          )}
        </motion.div>

        {/* View all link - bottom position for mobile */}
        {viewAllLink && (
          <div className="flex justify-center mt-8 sm:hidden">
            <Link
              href={viewAllLink}
              className={`flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`}
            >
              <span>View All</span>
              <ChevronRight className="h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>
        )}
      </div>
    </section>
  );
});

export default ProductSection;
