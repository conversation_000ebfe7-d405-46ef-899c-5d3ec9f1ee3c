#!/usr/bin/env python3
"""
Test script for Product Weight Scraper
Tests the scraper with a small subset of products
"""

import pandas as pd
import json
import os
from datetime import datetime
from product_weight_scraper import ProductWeightScraper, Product

def create_test_csv():
    """Create a small test CSV with sample products"""
    test_data = [
        {
            'Name': 'Haier Hood Vision Series T-Shaped HIH-T90HM-V',
            'Category': 'Hood',
            'Subcategory': 'Vision Series',
            'Brand': 'Haier',
            'Price': 24990
        },
        {
            'Name': 'Qubo Smart Cam 360 Q100 WiFi CCTV Security Camera',
            'Category': 'Smart Home Accessories',
            'Subcategory': '',
            'Brand': 'Qubo',
            'Price': 1590
        },
        {
            'Name': 'Godrej Nav-tal 6 Levers Long Shackle Furniture Lock',
            'Category': 'Furniture Locks',
            'Subcategory': 'Furniture Locks',
            'Brand': 'Godrej',
            'Price': 540
        },
        {
            'Name': 'Tagus SS 304 Dish Rack 600mm Kitchen Accessory',
            'Category': 'Kitchen Accessories',
            'Subcategory': '',
            'Brand': 'Tagus',
            'Price': 1319
        }
    ]
    
    df = pd.DataFrame(test_data)
    test_file = 'test_products.csv'
    df.to_csv(test_file, index=False)
    print(f"Created test CSV: {test_file}")
    return test_file

def test_weight_extraction():
    """Test weight extraction functionality"""
    scraper = ProductWeightScraper()
    
    test_texts = [
        "Product weight: 5.2 kg",
        "Net weight 1200 grams",
        "Item weighs 3.5 pounds",
        "Weight: 800g",
        "Gross weight: 2.1 kilograms",
        "No weight information here",
        "The product dimensions are 30x20x15 cm and weighs 4.8 kg"
    ]
    
    print("Testing weight extraction:")
    print("-" * 40)
    
    for text in test_texts:
        weight = scraper.extract_weight_from_text(text)
        print(f"Text: {text}")
        print(f"Extracted weight: {weight}")
        print()

def test_product_name_cleaning():
    """Test product name cleaning functionality"""
    scraper = ProductWeightScraper()
    
    test_names = [
        "Haier Hood Vision Series T-Shaped Tpe T-Shaped Motor CopperMotor Motor CopperMotor MaximumAirFlow HIH-T90HM-V",
        "Qubo E27 LED Wi-Fi BT 16 Million Colours Voice Control Alexa OK Google Smart Bulb 2",
        "Nav-tal 6 Levers - Long Shackle (2 keys) Furniture Locks Furniture Locks",
        "Tandem Partition Thali Stand"
    ]
    
    print("Testing product name cleaning:")
    print("-" * 40)
    
    for name in test_names:
        cleaned = scraper.clean_product_name(name)
        print(f"Original: {name}")
        print(f"Cleaned:  {cleaned}")
        print()

def test_single_product():
    """Test scraping for a single product"""
    print("Testing single product scraping:")
    print("-" * 40)
    
    # Create a test product
    test_product = Product(
        name="Haier Washing Machine HW80-IM12929C",
        category="Washing Machines",
        subcategory="",
        brand="Haier",
        price=34730
    )
    
    scraper = ProductWeightScraper()
    
    # Test weight extraction (without actually scraping)
    print(f"Test product: {test_product.name}")
    print(f"Brand: {test_product.brand}")
    print(f"Cleaned name: {scraper.clean_product_name(test_product.name)}")
    
    # You can uncomment the line below to test actual scraping
    # result = scraper.scrape_product_weight(test_product)
    # print(f"Scraped weight: {result.weight}")

def test_csv_loading():
    """Test CSV loading functionality"""
    print("Testing CSV loading:")
    print("-" * 40)
    
    # Create test CSV
    test_file = create_test_csv()
    
    try:
        scraper = ProductWeightScraper(test_file)
        scraper.load_products()
        
        print(f"Loaded {len(scraper.products)} products")
        
        # Display loaded products
        for i, product in enumerate(scraper.products, 1):
            print(f"{i}. {product.name} ({product.brand}) - ₹{product.price}")
        
        # Test grouping by brand
        brands = {}
        for product in scraper.products:
            if product.brand not in brands:
                brands[product.brand] = []
            brands[product.brand].append(product)
        
        print(f"\nProducts by brand:")
        for brand, products in brands.items():
            print(f"  {brand}: {len(products)} products")
    
    finally:
        # Clean up test file
        if os.path.exists(test_file):
            os.remove(test_file)
            print(f"\nCleaned up test file: {test_file}")

def test_configuration():
    """Test configuration loading"""
    print("Testing configuration:")
    print("-" * 40)
    
    try:
        from config import SCRAPING_CONFIG, USER_AGENTS, SEARCH_SITES
        print("✓ Configuration loaded successfully")
        print(f"  Max workers: {SCRAPING_CONFIG.get('max_workers', 'Not set')}")
        print(f"  Batch size: {SCRAPING_CONFIG.get('batch_size', 'Not set')}")
        print(f"  User agents: {len(USER_AGENTS)} available")
        print(f"  Search sites: {list(SEARCH_SITES.keys())}")
    except ImportError:
        print("⚠ Configuration file not found - using defaults")

def run_all_tests():
    """Run all tests"""
    print("=" * 50)
    print("PRODUCT WEIGHT SCRAPER - TEST SUITE")
    print("=" * 50)
    print()
    
    test_configuration()
    print()
    
    test_weight_extraction()
    print()
    
    test_product_name_cleaning()
    print()
    
    test_csv_loading()
    print()
    
    test_single_product()
    print()
    
    print("=" * 50)
    print("ALL TESTS COMPLETED")
    print("=" * 50)

if __name__ == "__main__":
    run_all_tests()
