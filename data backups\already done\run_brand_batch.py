#!/usr/bin/env python3
"""
Brand-specific batch runner for Product Weight Scraper
Allows running the scraper for specific brands only
"""

import sys
import os
import argparse
import json
from datetime import datetime
from product_weight_scraper import ProductWeightScraper

def main():
    parser = argparse.ArgumentParser(description='Run product weight scraper for specific brands')
    parser.add_argument('--brands', nargs='+', help='Brands to process (e.g., Haier Godrej)', 
                       choices=['Haier', 'Godrej', 'Qubo', 'Tagus'], default=['Haier'])
    parser.add_argument('--csv', help='CSV file path', 
                       default='products_export_20250709_161453.csv')
    parser.add_argument('--workers', type=int, help='Number of worker threads', default=2)
    parser.add_argument('--batch-size', type=int, help='Batch size for processing', default=10)
    parser.add_argument('--output', help='Output directory', default='results')
    parser.add_argument('--dry-run', action='store_true', help='Show what would be processed without scraping')
    
    args = parser.parse_args()
    
    # Check if CSV file exists
    if not os.path.exists(args.csv):
        print(f"Error: CSV file not found: {args.csv}")
        sys.exit(1)
    
    # Create output directory
    os.makedirs(args.output, exist_ok=True)
    
    # Initialize scraper
    scraper = ProductWeightScraper(args.csv)
    
    try:
        # Load products
        scraper.load_products()
        print(f"Loaded {len(scraper.products)} products from {args.csv}")
        
        # Filter products by requested brands
        all_brands = list(set(product.brand for product in scraper.products))
        print(f"Available brands: {all_brands}")
        
        requested_brands = args.brands
        print(f"Requested brands: {requested_brands}")
        
        # Validate requested brands
        invalid_brands = [b for b in requested_brands if b not in all_brands]
        if invalid_brands:
            print(f"Warning: Invalid brands specified: {invalid_brands}")
            requested_brands = [b for b in requested_brands if b in all_brands]
        
        if not requested_brands:
            print("Error: No valid brands to process")
            sys.exit(1)
        
        # Show what will be processed
        for brand in requested_brands:
            brand_products = [p for p in scraper.products if p.brand == brand]
            print(f"\n{brand}: {len(brand_products)} products")
            
            if args.dry_run:
                print("Sample products:")
                for i, product in enumerate(brand_products[:5], 1):
                    print(f"  {i}. {product.name[:60]}...")
                if len(brand_products) > 5:
                    print(f"  ... and {len(brand_products) - 5} more")
        
        if args.dry_run:
            print(f"\nDry run completed. Use without --dry-run to start scraping.")
            return
        
        # Update scraper configuration
        scraper.config['max_workers'] = args.workers
        scraper.config['batch_size'] = args.batch_size
        
        print(f"\nStarting scraping with {args.workers} workers, batch size {args.batch_size}")
        print("=" * 60)
        
        # Process each requested brand
        for brand in requested_brands:
            print(f"\nProcessing brand: {brand}")
            print("-" * 40)
            
            brand_results = scraper.process_brand_batch(brand, max_workers=args.workers)
            scraper.scraped_data.extend(brand_results)
            
            # Save brand-specific results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            brand_file = os.path.join(args.output, f"weights_{brand.lower()}_{timestamp}.json")
            
            brand_dict = {
                'brand': brand,
                'processed_at': datetime.now().isoformat(),
                'total_products': len(brand_results),
                'products_with_weight': len([p for p in brand_results if p.weight]),
                'success_rate': len([p for p in brand_results if p.weight]) / len(brand_results) * 100 if brand_results else 0,
                'products': [
                    {
                        'name': p.name,
                        'category': p.category,
                        'subcategory': p.subcategory,
                        'brand': p.brand,
                        'price': p.price,
                        'weight': p.weight,
                        'source_url': p.source_url,
                        'scraped_at': p.scraped_at
                    } for p in brand_results
                ]
            }
            
            with open(brand_file, 'w', encoding='utf-8') as f:
                json.dump(brand_dict, f, indent=2, ensure_ascii=False)
            
            print(f"Results saved to: {brand_file}")
            
            # Print summary
            with_weight = len([p for p in brand_results if p.weight])
            success_rate = with_weight / len(brand_results) * 100 if brand_results else 0
            print(f"Summary: {with_weight}/{len(brand_results)} products with weight ({success_rate:.1f}%)")
        
        # Save combined results
        if len(requested_brands) > 1:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            combined_file = os.path.join(args.output, f"weights_combined_{timestamp}.json")
            scraper.save_results(combined_file)
            print(f"\nCombined results saved to: {combined_file}")
        
        print("\n" + "=" * 60)
        print("SCRAPING COMPLETED!")
        
        # Final summary
        total_products = len(scraper.scraped_data)
        total_with_weight = len([p for p in scraper.scraped_data if p.weight])
        overall_success = total_with_weight / total_products * 100 if total_products else 0
        
        print(f"Overall summary:")
        print(f"  Total products processed: {total_products}")
        print(f"  Products with weight: {total_with_weight}")
        print(f"  Overall success rate: {overall_success:.1f}%")
        
        # Brand breakdown
        print(f"\nBy brand:")
        for brand in requested_brands:
            brand_products = [p for p in scraper.scraped_data if p.brand == brand]
            brand_with_weight = len([p for p in brand_products if p.weight])
            brand_rate = brand_with_weight / len(brand_products) * 100 if brand_products else 0
            print(f"  {brand}: {brand_with_weight}/{len(brand_products)} ({brand_rate:.1f}%)")
        
    except KeyboardInterrupt:
        print("\n\nScraping interrupted by user")
        if scraper.scraped_data:
            # Save partial results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            partial_file = os.path.join(args.output, f"weights_partial_{timestamp}.json")
            scraper.save_results(partial_file)
            print(f"Partial results saved to: {partial_file}")
    except Exception as e:
        print(f"\nError during scraping: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
