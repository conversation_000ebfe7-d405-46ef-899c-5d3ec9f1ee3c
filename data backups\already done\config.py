"""
Configuration file for Product Weight Scraper
Modify these settings to customize the scraping behavior
"""

# File paths
CSV_FILE_PATH = "products_export_20250709_161453.csv"
OUTPUT_DIR = "results"

# Scraping settings
SCRAPING_CONFIG = {
    # Number of concurrent threads per brand
    'max_workers': 2,
    
    # Number of products to process in each batch
    'batch_size': 10,
    
    # Request timeout in seconds
    'request_timeout': 10,
    
    # Delay ranges (in seconds)
    'request_delay_min': 1,
    'request_delay_max': 3,
    'batch_delay_min': 5,
    'batch_delay_max': 10,
    'brand_delay_min': 10,
    'brand_delay_max': 20,
    
    # Maximum number of product pages to check per search
    'max_product_pages': 3,
    
    # Enable/disable specific sites
    'enabled_sites': {
        'amazon': True,
        'flipkart': True,
        'snapdeal': True
    }
}

# User agents for rotation (helps avoid detection)
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON><PERSON><PERSON>, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:89.0) Gecko/20100101 Firefox/89.0'
]

# E-commerce site configurations
SEARCH_SITES = {
    'amazon': {
        'url': 'https://www.amazon.in/s?k={query}',
        'product_link_selectors': [
            'a.s-link-style',
            'h2.a-size-mini a',
            '.s-result-item a[href*="/dp/"]'
        ],
        'weight_selectors': [
            '[data-feature-name="detailBullets"] span:contains("Weight")',
            '.a-section .a-spacing-small span:contains("Weight")',
            '#feature-bullets ul li span:contains("Weight")',
            '.a-unordered-list .a-list-item:contains("Weight")',
            '#productDetails_detailBullets_sections1 tr:contains("Weight")',
            '.a-keyvalue:contains("Weight")'
        ],
        'base_url': 'https://www.amazon.in'
    },
    'flipkart': {
        'url': 'https://www.flipkart.com/search?q={query}',
        'product_link_selectors': [
            'a._1fQZEK',
            '._4rR01T a',
            '.s1Q9rs a'
        ],
        'weight_selectors': [
            '._1AN87F:contains("Weight")',
            '.row:contains("Weight") ._21lJbe',
            '._1hKmbr:contains("Weight")',
            '.col:contains("Weight") span',
            '._2418kt:contains("Weight")',
            '.row:contains("Net Quantity") ._21lJbe'
        ],
        'base_url': 'https://www.flipkart.com'
    },
    'snapdeal': {
        'url': 'https://www.snapdeal.com/search?keyword={query}',
        'product_link_selectors': [
            '.product-tuple-listing a',
            '.product-desc-rating a'
        ],
        'weight_selectors': [
            '.spec-body:contains("Weight")',
            '.detailssubbox:contains("Weight")',
            '.product-spec:contains("Weight")',
            '.product-specification:contains("Weight")'
        ],
        'base_url': 'https://www.snapdeal.com'
    }
}

# Weight extraction patterns (regex)
WEIGHT_PATTERNS = [
    r'(\d+(?:\.\d+)?)\s*(?:kg|kilogram|kilograms)\b',
    r'(\d+(?:\.\d+)?)\s*(?:g|gram|grams|gm)\b',
    r'(\d+(?:\.\d+)?)\s*(?:lb|lbs|pound|pounds)\b',
    r'(\d+(?:\.\d+)?)\s*(?:oz|ounce|ounces)\b',
    r'weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)\b',
    r'net\s*weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)\b',
    r'gross\s*weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)\b',
    r'item\s*weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)\b',
    r'product\s*weight[:\s]*(\d+(?:\.\d+)?)\s*(?:kg|g|lb|oz|gm)\b'
]

# Words to remove from product names for better search
STOP_WORDS = [
    'the', 'and', 'or', 'with', 'for', 'in', 'on', 'at', 'to', 'from',
    'by', 'of', 'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been',
    'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
    'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
    'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
]

# Logging configuration
LOGGING_CONFIG = {
    'level': 'INFO',  # DEBUG, INFO, WARNING, ERROR, CRITICAL
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'file': 'scraper.log',
    'console': True
}

# Output configuration
OUTPUT_CONFIG = {
    'save_intermediate': True,  # Save results after each brand
    'save_summary': True,       # Save summary statistics
    'indent_json': 2,           # JSON indentation
    'ensure_ascii': False       # Allow unicode characters in JSON
}

# Brand processing order (optional - if you want to prioritize certain brands)
BRAND_PRIORITY = [
    'Haier',
    'Godrej', 
    'Qubo',
    'Tagus'
]

# Advanced settings
ADVANCED_CONFIG = {
    # Retry failed requests
    'max_retries': 3,
    'retry_delay': 5,
    
    # Skip products that already have weight data
    'skip_existing_weights': False,
    
    # Maximum search query length
    'max_query_length': 100,
    
    # Minimum confidence score for weight extraction
    'min_confidence': 0.7,
    
    # Enable proxy rotation (if you have proxies)
    'use_proxies': False,
    'proxy_list': []
}
