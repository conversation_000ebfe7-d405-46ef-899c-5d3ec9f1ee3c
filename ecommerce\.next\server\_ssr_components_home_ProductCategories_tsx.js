"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_components_home_ProductCategories_tsx";
exports.ids = ["_ssr_components_home_ProductCategories_tsx"];
exports.modules = {

/***/ "(ssr)/./components/home/<USER>":
/*!***********************************************!*\
  !*** ./components/home/<USER>
  \***********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronRight!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/skeleton */ \"(ssr)/./components/ui/skeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst ProductCategories = ({ categories, title = \"Shop by Category\", subtitle, accentColor = \"primary\", variant = \"section\", showTitle = true, showViewAll = true, maxCategories = 12 })=>{\n    const pathName = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [isLoading, setIsLoading] = react__WEBPACK_IMPORTED_MODULE_1___default().useState(true);\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"ProductCategories.useEffect\": ()=>{\n            // Set loading to false when categories are available\n            if (categories && Array.isArray(categories)) {\n                setIsLoading(false);\n            }\n        }\n    }[\"ProductCategories.useEffect\"], [\n        categories\n    ]);\n    // Default categories if none are provided or if the array is empty\n    const defaultCategories = [\n        {\n            id: 1,\n            name: \"Smart Locks\",\n            slug: \"smart-locks\",\n            image_url: \"https://placehold.co/400x400/2ECC71/FFFFFF?text=Smart+Locks\"\n        },\n        {\n            id: 2,\n            name: \"Security Cameras\",\n            slug: \"security-cameras\",\n            image_url: \"https://placehold.co/400x400/3498DB/FFFFFF?text=Security+Cameras\"\n        },\n        {\n            id: 3,\n            name: \"Home Automation\",\n            slug: \"home-automation\",\n            image_url: \"https://placehold.co/400x400/9B59B6/FFFFFF?text=Home+Automation\"\n        },\n        {\n            id: 4,\n            name: \"Lighting\",\n            slug: \"lighting\",\n            image_url: \"https://placehold.co/400x400/F1C40F/FFFFFF?text=Lighting\"\n        },\n        {\n            id: 5,\n            name: \"Sensors\",\n            slug: \"sensors\",\n            image_url: \"https://placehold.co/400x400/E74C3C/FFFFFF?text=Sensors\"\n        },\n        {\n            id: 6,\n            name: \"Alarms\",\n            slug: \"alarms\",\n            image_url: \"https://placehold.co/400x400/1ABC9C/FFFFFF?text=Alarms\"\n        }\n    ];\n    // Show skeleton loader when categories are loading\n    if (isLoading && variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-background/95 backdrop-blur-sm border-b border-border shadow-sm\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-3 sm:px-4 lg:px-6 py-3 sm:py-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"block md:hidden\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                            children: Array.from({\n                                length: 8\n                            }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-shrink-0 flex flex-col items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 rounded-2xl bg-muted animate-pulse mb-2\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 101,\n                                            columnNumber: 19\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_skeleton__WEBPACK_IMPORTED_MODULE_4__.Skeleton, {\n                                            className: \"w-12 h-3\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 102,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 100,\n                                    columnNumber: 17\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 98,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"hidden md:block space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                children: Array.from({\n                                    length: 12\n                                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col items-center fade-in-professional\",\n                                        style: {\n                                            animationDelay: `${index * 0.05}s`\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 rounded-2xl skeleton-professional mb-2 border border-border/80 shadow-md\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 114,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-12 lg:w-14 xl:w-16 h-3 rounded skeleton-professional\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 115,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 113,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center gap-2 mb-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-20 h-3 rounded skeleton-professional opacity-60\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-1 h-px bg-border\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 124,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 122,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                        children: Array.from({\n                                            length: 8\n                                        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex-shrink-0 flex flex-col items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-12 h-12 lg:w-14 lg:h-14 rounded-xl skeleton-professional mb-1 border border-border/60 shadow-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-8 lg:w-10 h-2 rounded skeleton-professional\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 130,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 19\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 126,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 95,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 94,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Don't render navigation if no categories\n    if (!categories || !Array.isArray(categories) || categories.length === 0) {\n        if (variant === \"navigation\") {\n            return null;\n        }\n    }\n    // Use provided categories if available, otherwise use default categories\n    // Make sure we have a valid array to work with\n    const effectiveCategories = Array.isArray(categories) && categories.length > 0 ? categories : defaultCategories;\n    // Process categories to ensure proper image handling\n    const categoriesWithImages = effectiveCategories.map((category)=>{\n        // Use image_url from backend API if available, otherwise fallback to image field or generate placeholder\n        let imageUrl = category.image_url || category.image;\n        // If no image is available from the backend, generate a colored placeholder immediately\n        if (!imageUrl) {\n            const colors = [\n                '2ECC71',\n                '3498DB',\n                '9B59B6',\n                'F1C40F',\n                'E74C3C',\n                '1ABC9C',\n                'E67E22',\n                '34495E',\n                '95A5A6',\n                'F39C12',\n                'D35400',\n                '8E44AD'\n            ];\n            const colorIndex = category.id % colors.length;\n            const color = colors[colorIndex];\n            const categoryText = encodeURIComponent(category.name);\n            imageUrl = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n        }\n        return {\n            ...category,\n            image_url: imageUrl,\n            // Add a fallback image path for onError handler\n            fallbackImage: `/assets/products/product-placeholder.svg`\n        };\n    });\n    // Determine accent color classes\n    const accentClasses = {\n        primary: {\n            bg: \"bg-theme-accent-primary/20\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/5\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        },\n        secondary: {\n            bg: \"bg-theme-accent-secondary/30\",\n            text: \"text-theme-accent-secondary\",\n            line: \"bg-theme-accent-secondary\",\n            gradient: \"from-theme-accent-secondary/5\",\n            activeBg: \"bg-theme-accent-secondary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-secondary/10\"\n        },\n        tertiary: {\n            bg: \"bg-theme-accent-primary/30\",\n            text: \"text-theme-accent-primary\",\n            line: \"bg-theme-accent-primary\",\n            gradient: \"from-theme-accent-primary/10\",\n            activeBg: \"bg-theme-accent-primary\",\n            activeText: \"text-white\",\n            hoverBg: \"hover:bg-theme-accent-primary/10\"\n        }\n    };\n    // Enhanced animation variants\n    const containerVariants = {\n        hidden: {\n            opacity: 0\n        },\n        show: {\n            opacity: 1,\n            transition: {\n                staggerChildren: 0.05,\n                delayChildren: 0.1\n            }\n        }\n    };\n    const itemVariants = {\n        hidden: {\n            opacity: 0,\n            y: 30,\n            scale: 0.8,\n            rotateY: -15\n        },\n        show: {\n            opacity: 1,\n            y: 0,\n            scale: 1,\n            rotateY: 0,\n            transition: {\n                duration: 0.6,\n                ease: \"easeOut\",\n                type: \"spring\",\n                stiffness: 120,\n                damping: 20\n            }\n        }\n    };\n    const categoryHoverVariants = {\n        hover: {\n            scale: 1.05,\n            y: -5,\n            rotateY: 5,\n            transition: {\n                duration: 0.3,\n                ease: \"easeOut\"\n            }\n        },\n        tap: {\n            scale: 0.95\n        }\n    };\n    // Navigation variant - Professional Amazon/Flipkart style layout\n    if (variant === \"navigation\") {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n            className: \"w-full bg-background/98 backdrop-blur-md border-b border-border/60 shadow-sm relative\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute top-0 left-0 right-0 h-0.5 bg-gradient-to-r from-theme-accent-primary via-theme-accent-secondary to-theme-accent-primary opacity-60\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"container mx-auto px-3 sm:px-4 lg:px-6 xl:px-8 py-3 sm:py-4 lg:py-5\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"block md:hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4 overflow-x-auto scrollbar-hide pb-2 -mx-3 px-3\",\n                                children: categoriesWithImages.map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-shrink-0 group\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col items-center transition-all duration-300 group-active:scale-95\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative overflow-hidden rounded-2xl w-16 h-16 bg-gradient-to-br from-muted/50 to-muted shadow-lg transition-all duration-300 group-hover:shadow-xl mb-2 border border-border/50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                src: category.image_url,\n                                                                alt: category.name,\n                                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                                onError: (e)=>{\n                                                                    const imgElement = e.target;\n                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                        return;\n                                                                    }\n                                                                    const colors = [\n                                                                        '2ECC71',\n                                                                        '3498DB',\n                                                                        '9B59B6',\n                                                                        'F1C40F',\n                                                                        'E74C3C',\n                                                                        '1ABC9C',\n                                                                        'E67E22',\n                                                                        '34495E',\n                                                                        '95A5A6',\n                                                                        'F39C12',\n                                                                        'D35400',\n                                                                        '8E44AD'\n                                                                    ];\n                                                                    const colorIndex = category.id % colors.length;\n                                                                    const color = colors[colorIndex];\n                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                    imgElement.onerror = null;\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"absolute inset-0 bg-gradient-to-t from-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 275,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs font-medium text-foreground text-center line-clamp-2 max-w-[4.5rem] leading-tight\",\n                                                        children: category.name\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 301,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 272,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, category.id || `category-${index}`, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 269,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 268,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"hidden md:block space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"grid grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-3 lg:gap-4\",\n                                    children: categoriesWithImages.slice(0, 12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"group fade-in-professional\",\n                                            style: {\n                                                animationDelay: `${index * 0.05}s`\n                                            },\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"relative overflow-hidden rounded-2xl w-16 h-16 lg:w-18 lg:h-18 xl:w-20 xl:h-20 bg-card shadow-md group-hover:shadow-lg mb-2 border border-border group-hover:border-theme-accent-primary/30 transition-all duration-300\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-1 rounded-xl overflow-hidden\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                        src: category.image_url,\n                                                                        alt: category.name,\n                                                                        className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                        onError: (e)=>{\n                                                                            const imgElement = e.target;\n                                                                            if (imgElement.src.includes('placehold.co')) {\n                                                                                return;\n                                                                            }\n                                                                            const colors = [\n                                                                                '2ECC71',\n                                                                                '3498DB',\n                                                                                '9B59B6',\n                                                                                'F1C40F',\n                                                                                'E74C3C',\n                                                                                '1ABC9C',\n                                                                                'E67E22',\n                                                                                '34495E',\n                                                                                '95A5A6',\n                                                                                'F39C12',\n                                                                                'D35400',\n                                                                                '8E44AD'\n                                                                            ];\n                                                                            const colorIndex = category.id % colors.length;\n                                                                            const color = colors[colorIndex];\n                                                                            const categoryText = encodeURIComponent(category.name);\n                                                                            imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                            imgElement.onerror = null;\n                                                                        }\n                                                                    }, void 0, false, {\n                                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                        lineNumber: 323,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 322,\n                                                                    columnNumber: 25\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"absolute inset-0 bg-gradient-to-t from-theme-accent-primary/20 via-transparent to-theme-accent-primary/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 345,\n                                                                    columnNumber: 25\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 320,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-medium text-foreground group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[4rem] lg:max-w-[4.5rem] xl:max-w-[5rem] leading-tight\",\n                                                            children: category.name\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 349,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 318,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, category.id || `category-${index}`, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 316,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 13\n                                }, undefined),\n                                categoriesWithImages.length > 12 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs font-medium text-muted-foreground uppercase tracking-wide\",\n                                                    children: \"More Categories\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-1 h-px bg-gradient-to-r from-border to-transparent\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 363,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex gap-2 overflow-x-auto scrollbar-hide pb-1 -mx-1 px-1\",\n                                            children: categoriesWithImages.slice(12).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex-shrink-0 group\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                                        href: `/products/categories/${category.slug}?callbackUrl=%2F${pathName}`,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex flex-col items-center transition-all duration-300 group-hover:-translate-y-0.5\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"relative overflow-hidden rounded-xl w-12 h-12 lg:w-14 lg:h-14 bg-card shadow-sm group-hover:shadow-md mb-1 border border-border group-hover:border-theme-accent-primary/40 transition-all duration-300\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0.5 rounded-lg overflow-hidden\",\n                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                                                src: category.image_url,\n                                                                                alt: category.name,\n                                                                                className: \"w-full h-full object-cover transition-transform duration-300 group-hover:scale-110\",\n                                                                                onError: (e)=>{\n                                                                                    const imgElement = e.target;\n                                                                                    if (imgElement.src.includes('placehold.co')) {\n                                                                                        return;\n                                                                                    }\n                                                                                    const colors = [\n                                                                                        '2ECC71',\n                                                                                        '3498DB',\n                                                                                        '9B59B6',\n                                                                                        'F1C40F',\n                                                                                        'E74C3C',\n                                                                                        '1ABC9C',\n                                                                                        'E67E22',\n                                                                                        '34495E',\n                                                                                        '95A5A6',\n                                                                                        'F39C12',\n                                                                                        'D35400',\n                                                                                        '8E44AD'\n                                                                                    ];\n                                                                                    const colorIndex = category.id % colors.length;\n                                                                                    const color = colors[colorIndex];\n                                                                                    const categoryText = encodeURIComponent(category.name);\n                                                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                                                    imgElement.onerror = null;\n                                                                                }\n                                                                            }, void 0, false, {\n                                                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                                lineNumber: 372,\n                                                                                columnNumber: 31\n                                                                            }, undefined)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 371,\n                                                                            columnNumber: 29\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"absolute inset-0 bg-theme-accent-primary/15 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                            lineNumber: 393,\n                                                                            columnNumber: 29\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 370,\n                                                                    columnNumber: 27\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs font-medium text-muted-foreground group-hover:text-theme-accent-primary transition-colors duration-300 text-center line-clamp-2 max-w-[3rem] lg:max-w-[3.5rem] leading-tight\",\n                                                                    children: category.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                                    lineNumber: 395,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                            lineNumber: 369,\n                                                            columnNumber: 25\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                        lineNumber: 368,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                }, category.id || `category-more-${index}`, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 367,\n                                                    columnNumber: 21\n                                                }, undefined))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                            lineNumber: 365,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 360,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 312,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n            lineNumber: 262,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Section variant - full layout with background and decorations\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-12 sm:py-16 md:py-20 relative overflow-hidden w-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `absolute inset-0 bg-gradient-to-b ${accentClasses[accentColor].gradient} to-theme-homepage z-0 opacity-70`\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 415,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute top-1/3 right-[15%] w-32 h-32 rounded-full bg-theme-accent-secondary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 418,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"absolute bottom-1/3 left-[10%] w-24 h-24 rounded-full bg-theme-accent-primary/10 blur-xl opacity-50\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 419,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4 sm:px-6 lg:px-8 relative z-10 w-full max-w-full 2xl:max-w-[1536px]\",\n                children: [\n                    showTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center mb-8 sm:mb-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl sm:text-3xl md:text-4xl font-bold text-foreground mb-3 relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"relative z-10\",\n                                        children: title\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 426,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: `absolute -bottom-1 left-0 right-0 h-3 ${accentClasses[accentColor].bg} transform -rotate-1 z-0`\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 427,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 425,\n                                columnNumber: 13\n                            }, undefined),\n                            subtitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground text-center max-w-2xl mb-4\",\n                                children: subtitle\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 429,\n                                columnNumber: 26\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `w-16 sm:w-24 h-1 ${accentClasses[accentColor].line} rounded-full mt-1`\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 424,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                        variants: containerVariants,\n                        initial: \"hidden\",\n                        whileInView: \"show\",\n                        viewport: {\n                            once: true,\n                            margin: \"-100px\"\n                        },\n                        className: \"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6\",\n                        children: categoriesWithImages.slice(0, maxCategories).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.motion.div, {\n                                variants: itemVariants,\n                                whileHover: {\n                                    scale: 1.05,\n                                    y: -8,\n                                    rotateY: index % 2 === 0 ? 3 : -3,\n                                    transition: {\n                                        duration: 0.3,\n                                        ease: \"easeOut\"\n                                    }\n                                },\n                                whileTap: {\n                                    scale: 0.95\n                                },\n                                className: \"group cursor-pointer\",\n                                style: {\n                                    transformStyle: \"preserve-3d\",\n                                    perspective: \"1000px\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: `/products/categories/${category.slug}`,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative overflow-hidden rounded-xl aspect-square bg-muted shadow-md transition-all duration-300 group-hover:shadow-lg group-hover:shadow-theme-accent-primary/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-b from-transparent to-black/50 opacity-70 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 466,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: category.image_url,\n                                                alt: category.name,\n                                                className: \"absolute inset-0 w-full h-full object-cover transition-transform duration-500 group-hover:scale-110\",\n                                                onError: (e)=>{\n                                                    // Fallback to a colored placeholder if the backend image fails\n                                                    const imgElement = e.target;\n                                                    // Prevent infinite error loops\n                                                    if (imgElement.src.includes('placehold.co')) {\n                                                        return;\n                                                    }\n                                                    // Generate a colored placeholder based on category name\n                                                    const colors = [\n                                                        '2ECC71',\n                                                        '3498DB',\n                                                        '9B59B6',\n                                                        'F1C40F',\n                                                        'E74C3C',\n                                                        '1ABC9C',\n                                                        'E67E22',\n                                                        '34495E',\n                                                        '95A5A6',\n                                                        'F39C12',\n                                                        'D35400',\n                                                        '8E44AD'\n                                                    ];\n                                                    const colorIndex = category.id % colors.length;\n                                                    const color = colors[colorIndex];\n                                                    const categoryText = encodeURIComponent(category.name);\n                                                    imgElement.src = `https://placehold.co/400x400/${color}/FFFFFF?text=${categoryText}`;\n                                                    imgElement.onerror = null; // Prevent further error handling\n                                                }\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-x-0 bottom-0 p-3 z-20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-white font-medium text-sm sm:text-base text-shadow\",\n                                                    children: category.name\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                    lineNumber: 496,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 495,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 bg-gradient-to-t from-theme-accent-primary/30 via-theme-accent-primary/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 z-10\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute inset-0 rounded-xl border-2 border-theme-accent-primary/0 group-hover:border-theme-accent-primary/50 transition-all duration-300 z-20\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                        lineNumber: 464,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 463,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, category.id || `category-${index}`, false, {\n                                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                lineNumber: 444,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 435,\n                        columnNumber: 9\n                    }, undefined),\n                    showViewAll && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-center mt-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                            href: \"/shop\",\n                            className: `flex items-center ${accentClasses[accentColor].text} hover:text-theme-accent-hover group transition-all duration-300`,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    children: \"View All Categories\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronRight_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    className: \"h-4 w-4 ml-1 group-hover:translate-x-1 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                                    lineNumber: 518,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                        lineNumber: 512,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n                lineNumber: 421,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\home\\\\ProductCategories.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductCategories);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/home/<USER>");

/***/ }),

/***/ "(ssr)/./components/ui/skeleton.tsx":
/*!************************************!*\
  !*** ./components/ui/skeleton.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Skeleton: () => (/* binding */ Skeleton)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\nfunction Skeleton({ className, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_1__.cn)(\"animate-pulse rounded-md bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\Triumph\\\\ecommerce\\\\components\\\\ui\\\\skeleton.tsx\",\n        lineNumber: 8,\n        columnNumber: 5\n    }, this);\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL3NrZWxldG9uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFvQztBQUVwQyxTQUFTQyxTQUFTLEVBQ2hCQyxTQUFTLEVBQ1QsR0FBR0MsT0FDa0M7SUFDckMscUJBQ0UsOERBQUNDO1FBQ0NGLFdBQVdGLDhDQUFFQSxDQUFDLHFDQUFxQ0U7UUFDbEQsR0FBR0MsS0FBSzs7Ozs7O0FBR2Y7QUFFbUIiLCJzb3VyY2VzIjpbIkQ6XFxUcml1bXBoXFxlY29tbWVyY2VcXGNvbXBvbmVudHNcXHVpXFxza2VsZXRvbi50c3giXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgY24gfSBmcm9tIFwiLi4vLi4vbGliL3V0aWxzXCJcclxuXHJcbmZ1bmN0aW9uIFNrZWxldG9uKHtcclxuICBjbGFzc05hbWUsXHJcbiAgLi4ucHJvcHNcclxufTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTERpdkVsZW1lbnQ+KSB7XHJcbiAgcmV0dXJuIChcclxuICAgIDxkaXZcclxuICAgICAgY2xhc3NOYW1lPXtjbihcImFuaW1hdGUtcHVsc2Ugcm91bmRlZC1tZCBiZy1tdXRlZFwiLCBjbGFzc05hbWUpfVxyXG4gICAgICB7Li4ucHJvcHN9XHJcbiAgICAvPlxyXG4gIClcclxufVxyXG5cclxuZXhwb3J0IHsgU2tlbGV0b24gfVxyXG4iXSwibmFtZXMiOlsiY24iLCJTa2VsZXRvbiIsImNsYXNzTmFtZSIsInByb3BzIiwiZGl2Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/skeleton.tsx\n");

/***/ })

};
;