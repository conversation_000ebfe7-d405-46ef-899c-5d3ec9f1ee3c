{"name": "ecommerce", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate-logo-sizes": "node scripts/generate-logo-sizes.js", "generate-sitemap": "node scripts/generate-sitemap.js", "build:seo": "npm run generate-logo-sizes && npm run generate-sitemap && next build", "postbuild": "npm run generate-sitemap"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@tanstack/react-query": "^5.56.2", "@types/crypto-js": "^4.2.2", "@vercel/analytics": "^1.4.0", "axios": "^1.7.9", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "crypto-js": "^4.2.0", "embla-carousel": "^8.5.1", "embla-carousel-autoplay": "^8.5.1", "embla-carousel-class-names": "^8.5.1", "embla-carousel-react": "^8.5.1", "formik": "^2.4.6", "framer-motion": "^11.11.17", "input-otp": "^1.2.4", "lovable-tagger": "^1.0.15", "lucide-react": "^0.454.0", "next": "^15.0.2", "next-auth": "^4.24.10", "next-themes": "^0.3.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.3.5", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "sonner": "^1.5.0", "swiper": "^11.1.14", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.3", "yup": "^1.4.0"}, "devDependencies": {"@types/node": "22.9.0", "@types/react": "^18.3.12", "@types/react-dom": "^18", "eslint": "^8.57.0", "eslint-config-next": "^13.5.7", "postcss": "^8", "sharp": "^0.33.2", "tailwindcss": "^3.4.1", "typescript": "5.6.3"}}