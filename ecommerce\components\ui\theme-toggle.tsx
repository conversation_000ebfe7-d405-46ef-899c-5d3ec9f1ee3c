"use client"

import * as React from "react"
import { <PERSON>, Sun, Monitor } from "lucide-react"
import { useTheme } from "next-themes"

import { Button } from "./button"

interface ThemeToggleProps {
  variant?: "compact" | "expanded"
}

export function ThemeToggle({ variant = "compact" }: ThemeToggleProps) {
  const { theme, setTheme } = useTheme()

  if (variant === "expanded") {
    return (
      <div className="flex items-center justify-center space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setTheme("light")}
          className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
            theme === "light"
              ? "bg-theme-accent-primary/20 text-theme-accent-primary border border-theme-accent-primary/30"
              : "text-white hover:bg-white/10"
          }`}
          aria-label="Light theme"
        >
          <Sun className="h-4 w-4" />
          <span className="text-sm font-medium">Light</span>
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => setTheme("dark")}
          className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
            theme === "dark"
              ? "bg-theme-accent-primary/20 text-theme-accent-primary border border-theme-accent-primary/30"
              : "text-white hover:bg-white/10"
          }`}
          aria-label="Dark theme"
        >
          <Moon className="h-4 w-4" />
          <span className="text-sm font-medium">Dark</span>
        </Button>

        <Button
          variant="ghost"
          size="sm"
          onClick={() => setTheme("system")}
          className={`flex items-center space-x-2 px-4 py-2 rounded-xl transition-all duration-200 ${
            theme === "system"
              ? "bg-theme-accent-primary/20 text-theme-accent-primary border border-theme-accent-primary/30"
              : "text-white hover:bg-white/10"
          }`}
          aria-label="System theme"
        >
          <Monitor className="h-4 w-4" />
          <span className="text-sm font-medium">Auto</span>
        </Button>
      </div>
    )
  }

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={() => setTheme(theme === "light" ? "dark" : "light")}
      className="inline-flex items-center rounded-full justify-center p-2 sm:p-3 md:p-2 hover:bg-white/10 text-sm font-medium leading-none text-white transition-all duration-200 active:scale-95 mobile-touch-target"
      aria-label="Toggle theme"
    >
      <Sun className="h-5 w-5 rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0" />
      <Moon className="absolute h-5 w-5 rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100" />
      <span className="sr-only">Toggle theme</span>
    </Button>
  )
}
