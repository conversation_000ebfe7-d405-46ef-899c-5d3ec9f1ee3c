"use client";

import React from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import Link from "next/link";
import { MAIN_URL } from "@/constant/urls";

interface SmartHomeHeroProps {
  featuredProducts: any[];
}

const SmartHomeHero: React.FC<SmartHomeHeroProps> = ({ featuredProducts }) => {
  // Take the first two products for the hero section
  const primaryProduct = featuredProducts?.[1];
  const secondaryProduct = featuredProducts?.[2];

  if (!primaryProduct || !secondaryProduct) {
    return (
      <div className="w-full h-[500px] bg-gray-100 animate-pulse rounded-xl"></div>
    );
  }

  return (
    <section className="w-full py-8 relative overflow-hidden">
      {/* Decorative elements */}
      <div className="absolute top-[-50px] right-[10%] w-[150px] h-[150px] rounded-full bg-theme-accent-secondary/20 blur-xl"></div>
      <div className="absolute bottom-[-30px] left-[5%] w-[100px] h-[100px] rounded-full bg-theme-accent-primary/10 blur-xl"></div>
      
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Left column - Primary product */}
          <motion.div 
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="bg-[#f5f2ed] rounded-2xl p-6 relative overflow-hidden"
          >
            <div className="absolute top-[-20px] right-[-20px] w-[100px] h-[100px] rounded-full bg-theme-accent-secondary/20"></div>
            
            <div className="flex flex-col h-full">
              <div className="mb-4">
                <h3 className="text-sm text-theme-text-primary/70 uppercase tracking-wider mb-2">Featured Product</h3>
                <h2 className="text-2xl md:text-3xl font-bold text-theme-text-primary">{primaryProduct.name}</h2>
                <p className="text-theme-text-primary/80 mt-2 line-clamp-2">{primaryProduct.description || "Smart home solution for modern living"}</p>
              </div>
              
              <div className="flex-grow relative h-[250px] md:h-[300px] my-4">
                <img 
                  src={MAIN_URL+primaryProduct.image} 
                  alt={primaryProduct.name}
                  className="absolute inset-0 w-full h-full object-contain"
                />
              </div>
              
              <div className="flex justify-between items-center mt-auto">
                <div>
                  <span className="text-theme-accent-primary font-bold text-xl">₹{primaryProduct.price}</span>
                </div>
                <Link href={`/product/${primaryProduct.slug}`}>
                  <Button className="bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full">
                    View Details <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
          
          {/* Right column - Secondary product */}
          <motion.div 
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="bg-[#e9e4dd] rounded-2xl p-6 relative overflow-hidden"
          >
            <div className="absolute bottom-[-30px] left-[-30px] w-[120px] h-[120px] rounded-full bg-theme-accent-primary/10"></div>
            
            <div className="flex flex-col h-full">
              <div className="mb-4">
                <h3 className="text-sm text-theme-text-primary/70 uppercase tracking-wider mb-2">New Arrival</h3>
                <h2 className="text-2xl md:text-3xl font-bold text-theme-text-primary">{secondaryProduct.name}</h2>
                <p className="text-theme-text-primary/80 mt-2 line-clamp-2">{secondaryProduct.description || "Innovative technology for your home"}</p>
              </div>
              
              <div className="flex-grow relative h-[250px] md:h-[300px] my-4">
                <img 
                  src={MAIN_URL+secondaryProduct.image} 
                  alt={secondaryProduct.name}
                  className="absolute inset-0 w-full h-full object-contain"
                />
              </div>
              
              <div className="flex justify-between items-center mt-auto">
                <div>
                  <span className="text-theme-accent-primary font-bold text-xl">₹{secondaryProduct.price}</span>
                </div>
                <Link href={`/product/${secondaryProduct.slug}`}>
                  <Button className="bg-theme-accent-primary hover:bg-theme-accent-hover text-white rounded-full">
                    View Details <ArrowRight className="ml-2 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default SmartHomeHero;
