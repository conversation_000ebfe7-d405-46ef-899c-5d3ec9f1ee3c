"use client";

import { useScroll, useTransform, useSpring, MotionValue } from "framer-motion";
import { useRef, RefObject } from "react";

// Animation variants for different scroll effects
export const scrollAnimationVariants = {
  // Fade in from bottom
  fadeInUp: {
    hidden: { 
      opacity: 0, 
      y: 60,
      scale: 0.95
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 100,
        damping: 20
      } 
    },
  },

  // Slide in from left
  slideInLeft: {
    hidden: { 
      opacity: 0, 
      x: -80,
      rotateY: -15
    },
    visible: { 
      opacity: 1, 
      x: 0,
      rotateY: 0,
      transition: { 
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 120,
        damping: 25
      } 
    },
  },

  // Slide in from right
  slideInRight: {
    hidden: { 
      opacity: 0, 
      x: 80,
      rotateY: 15
    },
    visible: { 
      opacity: 1, 
      x: 0,
      rotateY: 0,
      transition: { 
        duration: 0.8,
        ease: "easeOut",
        type: "spring",
        stiffness: 120,
        damping: 25
      } 
    },
  },

  // Scale in
  scaleIn: {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      rotateX: -20
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      rotateX: 0,
      transition: { 
        duration: 0.7,
        ease: "easeOut",
        type: "spring",
        stiffness: 150,
        damping: 20
      } 
    },
  },

  // Stagger container
  staggerContainer: {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  },

  // Stagger items
  staggerItem: {
    hidden: { 
      opacity: 0, 
      y: 30,
      scale: 0.9
    },
    visible: { 
      opacity: 1, 
      y: 0,
      scale: 1,
      transition: { 
        duration: 0.6,
        ease: "easeOut"
      } 
    },
  },
};

// Hover animation variants
export const hoverAnimationVariants = {
  // Gentle lift
  gentleLift: {
    hover: {
      scale: 1.03,
      y: -5,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    tap: {
      scale: 0.98
    }
  },

  // 3D tilt
  tilt3D: {
    hover: {
      scale: 1.05,
      y: -8,
      rotateY: 5,
      rotateX: 5,
      transition: {
        duration: 0.3,
        ease: "easeOut"
      }
    },
    tap: {
      scale: 0.95
    }
  },

  // Bounce
  bounce: {
    hover: {
      scale: 1.1,
      transition: {
        duration: 0.4,
        type: "spring",
        stiffness: 400,
        damping: 10
      }
    },
    tap: {
      scale: 0.9
    }
  },
};

// Custom hook for scroll-based parallax effects
export const useScrollParallax = (target?: RefObject<HTMLElement>) => {
  const ref = useRef<HTMLDivElement>(null);
  const targetRef = target || ref;

  const { scrollYProgress } = useScroll({
    target: targetRef,
    offset: ["start end", "end start"]
  });

  // Smooth spring animation for scroll progress
  const smoothProgress = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
    restDelta: 0.001
  });

  // Various parallax transforms
  const y = useTransform(smoothProgress, [0, 1], ["0%", "50%"]);
  const yReverse = useTransform(smoothProgress, [0, 1], ["0%", "-50%"]);
  const scale = useTransform(smoothProgress, [0, 1], [1, 1.1]);
  const opacity = useTransform(smoothProgress, [0, 0.5, 1], [0.8, 0.5, 0.2]);
  const rotate = useTransform(smoothProgress, [0, 1], [0, 360]);

  return {
    ref: targetRef,
    scrollYProgress: smoothProgress,
    transforms: {
      y,
      yReverse,
      scale,
      opacity,
      rotate
    }
  };
};

// Custom hook for intersection-based animations
export const useIntersectionAnimation = () => {
  const ref = useRef<HTMLDivElement>(null);

  return {
    ref,
    viewport: { once: true, margin: "-100px" },
    initial: "hidden",
    whileInView: "visible"
  };
};

// Utility function to get random animation variant
export const getRandomAnimationVariant = (variants: string[]) => {
  return variants[Math.floor(Math.random() * variants.length)];
};

// Predefined animation sets
export const animationSets = {
  hero: scrollAnimationVariants.scaleIn,
  section: scrollAnimationVariants.fadeInUp,
  alternating: [
    scrollAnimationVariants.slideInLeft,
    scrollAnimationVariants.slideInRight
  ],
  cards: scrollAnimationVariants.staggerContainer,
  cardItem: scrollAnimationVariants.staggerItem,
};
